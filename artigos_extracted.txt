🔎 Artigo 1
Título: Leveraging Large Language Models for Accurate Retrieval of Patient Information From Medical Reports: Systematic Evaluation Study
Fonte: JMIR AI, 2025
Resumo (traduzido)
O estudo investigou a aplicação de LLMs para a extração automatizada de informações estruturadas a partir de relatórios médicos não estruturados, utilizando o framework LangChain em Python. Foram avaliados GPT-4o, Llama 3/3.1, Gemma 2, Qwen 2/2.5 em tarefas como extração de dados demográficos, diagnósticos e farmacológicos. O GPT-4o alcançou a melhor performance (91,4% de acurácia). O trabalho também discute o uso de RAG (retrieval-augmented generation) para reduzir alucinações e problemas de dados desatualizados.
Palavras-chave
LLMs; LangChain; registros eletrônicos de saúde; mineração de dados; avaliação de modelos; digitalização da saúde.
Metodologia
Avaliação sistemática de múltiplos LLMs.
Uso de zero-shot prompting + armazenamento vetorial (vector DB).
Categorias: dados demográficos, detalhes diagnósticos e dados farmacológicos.
Métricas: acurácia, precisão, recall e F1-score.
Experimentos com PDFs de relatórios médicos reais.
Resultados
GPT-4o: 91,4% de acurácia (melhor geral).
Diferenças relevantes em precision e recall entre modelos.
Extração de nomes e idades foi mais desafiadora.
LLMs mostraram ganhos claros para tomada de decisão clínica.
RAG ajudou a corrigir alucinações e dados incompletos.
Conclusões
LLMs são promissores para integração em fluxos clínicos, mas ainda apresentam variabilidade de desempenho. É necessário ampliar o treinamento com datasets mais diversos e aplicar prompting avançado para maior generalização.
Linha de pesquisa
➡ Extração de informações clínicas estruturadas a partir de texto não estruturado em PEP/EHR via LLMs + RAG.
Relevância
⭐ Alta. Forte contribuição para ETL em saúde, RAG clínico e NLP aplicado a PEP.

[QUEBRA DE PÁGINA]

🔎 Artigo 2
Título: A Comparison of a Large Language Model vs Manual Chart Review for the Extraction of Data Elements From the Electronic Health Record
Fonte: Gastroenterology, 2024
Resumo (traduzido)
O estudo comparou GPT-4 (implementado via Microsoft Azure, PHI-compliant) com revisão manual de prontuários para extração de 8 elementos clínicos em relatórios de imagem de carcinoma hepatocelular (HCC). Incluiu 1101 relatórios de 753 pacientes no UCSF. O GPT-4 alcançou 93,4% de acurácia geral, variando de 88,6% (soma dos diâmetros de tumores) a 98,9% (metástases extra-hepáticas).
Palavras-chave
LLM; GPT-4; revisão manual; EHR; HCC; extração de dados clínicos.
Metodologia
1101 relatórios de TC/RM revisados manualmente (≈28h).
GPT-4 processou via few-shot prompting (56 laços, 20 registros cada).
Avaliação: acurácia, precisão, recall, especificidade, F1.
Comparação com gold standard humano.
Resultados
Acurácia total: 0,934.
Melhor desempenho em tarefas binárias (macroinvasão vascular, metástases).
Pior desempenho em medidas numéricas (diâmetro/soma).
Alta especificidade (acima de 0,9).
Reduziu tempo de revisão (2h com LLM vs 28h manual).
Conclusões
LLMs apresentam desempenho comparável à revisão manual em EHR, mas a complexidade da tarefa reduz sua precisão. Mostram potencial para acelerar pesquisas clínicas.
Linha de pesquisa
➡ Comparação LLM vs humano em revisão de prontuários médicos.
Relevância
⭐ Alta. Demonstra eficiência e viabilidade real do uso de LLMs em cenários clínicos.

[QUEBRA DE PÁGINA]

🔎 Artigo 3
Título: Toward a Large Language Model-Driven Medical Knowledge Retrieval and QA System: Framework Design and Evaluation
Fonte: Engineering, 2025
Resumo (traduzido)
O estudo apresenta o ERQA, framework de Recuperação e Perguntas & Respostas médicas baseado em LLMs + banco vetorial + repositório de literatura. Realizou pré-treinamento incremental em dados biomédicos. Testado em datasets COVID-19 e TripClick, atingiu métricas de ponta (NDCG@10 = 0,297, Recall@10 = 0,347, MRR = 0,370). Também obteve bom desempenho em sumarização (ROUGE-1 = 0,434) e QA (BLEU-1 = 7,851).
Palavras-chave
LLMs; recuperação de conhecimento; banco vetorial; QA médico.
Metodologia
Framework ERQA = LLM + vetor semântico + fine-tuning biomédico.
Avaliação em COVID-19 e TripClick datasets.
Métricas: NDCG, Recall, MRR, ROUGE, BLEU.
Resultados
Desempenho robusto em recuperação de literatura biomédica.
Boa adaptação em domínios diversos.
Framework mitigou problemas de hallucination com curadoria externa.
Conclusões
O ERQA representa avanço no acesso a conhecimento biomédico confiável via LLMs + QA, com forte impacto para suporte clínico.
Linha de pesquisa
➡ RAG + LLM para QA biomédico.
Relevância
⭐ Alta. Base teórica sólida para busca semântica + QA em saúde.

[QUEBRA DE PÁGINA]

🔎 Artigo 4
Título: A comprehensive evaluation of large language models for information extraction from unstructured electronic health records in residential aged care
Fonte: Computers in Biology and Medicine, 2025
Resumo (traduzido)
O estudo avaliou 17 LLMs (gerais e biomédicos) para extração de informações em notas de enfermagem de cuidados prolongados na Austrália. Foram aplicados RAG (LangChain, LlamaIndex) e few-shot prompting. LLaMA 3.1 teve melhor desempenho (88,58% acurácia). Resultados mostraram alta precisão, mas baixa robustez (≈4%). Fairness: 99,9%; bias: 0,11%.
Palavras-chave
LLMs; extração de informação; EHR; dados não estruturados; fairness; RAG.
Metodologia
Dados reais: notas de enfermagem (RAC).
Avaliação em tarefas NER e sumarização.
Testes de fairness, bias, robustez e relevância.
Comparação de few-shot (1–5).
Resultados
LLaMA 3.1 = 88,58% acurácia, 87,43% F1.
RAG > modelos out-of-the-box.
Melhor configuração: 3-shot (NER), 5-shot (sumarização).
Problema crítico: baixa robustez com ruído real dos dados.
Conclusões
LLMs são promissores para RAC, mas precisam de maior robustez para lidar com heterogeneidade da documentação clínica real.
Linha de pesquisa
➡ Avaliação holística de LLMs em notas de enfermagem.
Relevância
⭐ Média-Alta. Importante para fairness/bias em dados reais de saúde.

[QUEBRA DE PÁGINA]

🔎 Artigo 5
Título: Advancements in natural language processing: Implications, challenges, and future directions
Fonte: Telematics and Informatics Reports, 2024
Resumo (traduzido)
Revisão sistemática sobre avanços em NLP, especialmente modelos transformer e deep learning. Analisa implicações, desafios e direções futuras. Aplica metodologia PRISMA para mapear a literatura desde 2010. Identifica aplicações em sumarização, tradução automática, chatbots, análise de sentimentos, entre outros.
Palavras-chave
NLP; aprendizado profundo; modelos transformer; revisão sistemática.
Metodologia
Revisão sistemática com PRISMA.
Bases: PubMed, Scopus, Web of Science, Google Scholar.
Critérios: estudos em inglês desde 2010, peer-reviewed.
Análise temática + síntese narrativa.
Resultados
Avanços relevantes: BERT, GPT-3, GPT-4.
NLP já aplicado a múltiplas indústrias.
Desafios: viés, robustez, interpretação, escalabilidade.
Conclusões
NLP segue em rápida expansão, mas exige frameworks robustos para confiabilidade e explicabilidade.
Linha de pesquisa
➡ Panorama geral de NLP e LLMs.
Relevância
⭐ Média. Bom como base teórica geral, não específico para PEP.

[QUEBRA DE PÁGINA]

🔎 Artigo 7
Título: Assessing large language models for acute heart failure classification and information extraction from French clinical notes
Fonte: Computers in Biology and Medicine, 2025
Resumo (traduzido)
Este estudo avaliou LLMs para identificação de hospitalizações por insuficiência cardíaca aguda (AHF) e extração de informações clínicas em notas médicas francesas. O modelo biomédico DrLongformer superou o Qwen2-7B na classificação e extração. Qwen2-7B teve melhor desempenho na extração de variáveis quantitativas (peso, IMC). O tamanho do corpus (até 250 documentos) impactou o desempenho, e anotações longas prejudicaram o treinamento.
Palavras-chave
Insuficiência cardíaca; NLP; LLMs; extração de informação; classificação.
Metodologia
Corpus: 10.182 notas clínicas (Hospital Nantes, França).
Modelos: DrLongformer (biomédico) vs Qwen2-7B (geral).
Técnicas: fine-tuning supervisionado, few-shot prompting, chain-of-thought.
Estudo de ablação sobre volume de dados e características das anotações.
Resultados
DrLongformer: F1 = 0,878 (vs 0,80 Qwen2-7B).
Qwen2-7B melhor em extração quantitativa.
Saturação de ganhos após 250 documentos.
Anotações longas prejudicaram performance.
Conclusões
Modelos biomédicos superam modelos gerais em tarefas clínicas, mas LLMs menores on-premise podem ser úteis em hospitais por integração com EHRs.
Linha de pesquisa
➡ Classificação + IE (NER) para insuficiência cardíaca aguda em notas clínicas francesas.
Relevância
⭐ Alta. Excelente caso de NLP biomédico não estruturado aplicado a doença específica (cardiovascular).

[QUEBRA DE PÁGINA]

🔎 Artigo 8
Título: Assessing the performance of ChatGPT’s responses to questions related to epilepsy: A cross-sectional study on natural language processing and medical information retrieval
Fonte: Seizure: European Journal of Epilepsy, 2024
Resumo (traduzido)
O estudo avaliou a precisão das respostas do ChatGPT (GPT-3.5 vs GPT-4) a 57 perguntas comuns sobre epilepsia, baseadas no guia da Korean Epilepsy Society. Dois epileptologistas revisaram as respostas. Dos 57 itens, 40 de GPT-4 tiveram “valor educacional suficiente”, 16 foram “corretas mas incompletas” e apenas 1 foi “mista”. Nenhuma incorreta. GPT-4 superou GPT-3.5 e foi comparável ou superior ao guia oficial.
Palavras-chave
Epilepsia; IA; ChatGPT; NLP; recuperação de informação médica.
Metodologia
57 perguntas do guia oficial de epilepsia (traduzido).
Respostas geradas em GPT-3.5 e GPT-4.
Avaliação por 2 especialistas (10+ anos).
Classificação: valor educacional suficiente / correto mas incompleto / misto / incorreto.
Resultados
GPT-4: 70,1% das respostas com valor educacional suficiente.
Nenhuma incorreta.
Melhor desempenho que GPT-3.5.
Em algumas áreas, comparável ao guia médico oficial.
Conclusões
GPT-4 pode complementar a educação de pacientes/cuidadores em epilepsia, mas não substitui especialistas.
Linha de pesquisa
➡ Educação em saúde com LLMs em epilepsia.
Relevância
⭐ Média. Relevante para informação médica ao paciente, não tanto para ETL em EHR.

[QUEBRA DE PÁGINA]

🔎 Artigo 9
Título: AssistMED project: Transforming cardiology cohort characterisation from electronic health records through NLP – Algorithm design, preliminary results, and field prospects
Fonte: International Journal of Medical Informatics, 2024
Resumo (traduzido)
O projeto AssistMED desenvolveu uma ferramenta de NLP para caracterizar automaticamente coortes de pacientes em cardiologia a partir de EHRs em polonês. O algoritmo extraiu 56 condições clínicas, 16 grupos de medicamentos (com dosagem) e 15 parâmetros ecocardiográficos de 400 pacientes. Os resultados mostraram desempenho comparável à anotação manual.
Palavras-chave
NLP; EHR; cardiologia; mineração de texto; epidemiologia.
Metodologia
Base: relatórios de alta hospitalar em polonês.
Extração: diagnósticos, medicamentos (dosagem), parâmetros ecocardiográficos.
Comparação com revisão manual.
Pipeline em spaCy com base de conhecimento clínico local.
Resultados
Sem diferenças estatísticas vs humanos.
Erros vieram de: falhas de contexto, erros humanos e erros aleatórios do algoritmo.
Algoritmo estruturou dados em formato amigável a pesquisadores.
Conclusões
NLP é viável para acelerar caracterização de coortes em cardiologia. Futuro: integrar LLMs para maior precisão.
Linha de pesquisa
➡ NLP aplicado à extração clínica em cardiologia (EHR polonês).
Relevância
⭐ Alta. Caso sólido de ETL automatizado em cardiologia.

🔎 Artigo 10
Título: A survey on biomedical automatic text summarization with large language models
Fonte: Information Processing and Management, 2025
Resumo (traduzido)
Revisão abrangente sobre sumarização automática de textos biomédicos (BATS) com LLMs. Analisa evolução desde métodos tradicionais (estatísticos, grafos), passando por RNN/CNN, até LLMs (BERT, GPT). Destaca datasets (PubMed, MIMIC-III, COVID-19) e métricas (ROUGE, BLEU, métricas clínicas). Aponta desafios: viés, escassez de dados anotados, precisão factual, privacidade.
Palavras-chave
Biomedicina; sumarização automática; LLMs; NLP; redes neurais.
Metodologia
Revisão histórica + análise comparativa de métodos.
Classificação: extração, abstração, híbridos.
Comparação de métricas tradicionais e emergentes (incluindo métricas clínicas).
Resultados
LLMs (GPT, BERT) mostram desempenho superior em biomedicina.
Ainda desafios com precisão factual e terminologia.
Potencial para diagnósticos personalizados e gestão do conhecimento.
Conclusões
LLMs estão redefinindo sumarização biomédica, mas exigem novas métricas e datasets adaptados.
Linha de pesquisa
➡ Sumarização biomédica (literatura + notas clínicas) com LLMs.
Relevância
⭐ Alta. Base teórica forte para processos de sumarização e triagem de literatura biomédica.

🔎 Artigo 11
Título: Can large language models reason about medical questions?
Fonte: Patterns, 2024
Resumo (traduzido)
Este artigo investigou se LLMs (GPT-3.5, Llama 2, etc.) conseguem raciocinar sobre questões médicas complexas, utilizando benchmarks como MedQA (USMLE), MedMCQA e PubMedQA. Foi avaliado o uso de chain-of-thought prompting, few-shot prompting e retrieval augmentation. Resultados mostram que LLMs (GPT-3.5 e Llama 2 70B) alcançam desempenho comparável ao de humanos em exames médicos, com GPT-4 e Med-PaLM 2 superando os benchmarks.
Palavras-chave
LLMs; raciocínio clínico; chain-of-thought; USMLE; avaliação médica.
Metodologia
Modelos: GPT-3.5, Codex, Llama 2, Med-PaLM 2, GPT-4.
Benchmarks: MedQA-USMLE, MedMCQA, PubMedQA.
Cenários: zero-shot, few-shot, CoT prompting, RAG.
Anotação de especialistas para avaliar raciocínio clínico.
Resultados
GPT-3.5: 60,2% no USMLE (nota mínima de aprovação = 60%).
Llama 2 70B: 62,5% no USMLE.
GPT-4: 86,1% (quase equivalente a médicos especialistas).
CoT + RAG melhoraram desempenho e reduziram alucinações.
Conclusões
LLMs já conseguem passar exames médicos, mas ainda apresentam viés, sensibilidade à ordem das opções e alucinações. São promissores para suporte clínico, mas precisam de protocolos rigorosos de validação.
Linha de pesquisa
➡ Raciocínio clínico e QA com LLMs em benchmarks médicos (USMLE, PubMedQA).
Relevância
⭐ Alta. Excelente para fundamentar capacidade de raciocínio médico de LLMs.

🔎 Artigo 13
Título: CDE-Mapper: Using retrieval-augmented language models for linking clinical data elements to controlled vocabularies
Fonte: Computers in Biology and Medicine, 2025
Resumo (traduzido)
Propõe o CDE-Mapper, um framework que usa RAG + LLMs para alinhar Clinical Data Elements (CDEs) com vocabulários controlados (SNOMED, LOINC, ICD, OMOP, etc.). Resolve inconsistências de representação e dificuldades em CDEs compostos. Em 4 datasets, obteve 7,2% de acurácia maior que métodos baseline.
Palavras-chave
CDEs; interoperabilidade; OMOP; RAG; padronização de dados.
Metodologia
Arquitetura modular: decomposição de queries + regras especialistas + ensemble retrievers.
Knowledge reservoir validado com human-in-loop.
Testado em 4 datasets diversos.
Resultados
+7,2% de acurácia vs baselines.
Melhoria significativa em CDEs compostos.
Baixo custo computacional com validação iterativa.
Conclusões
LLMs + RAG melhoram padronização e interoperabilidade clínica, acelerando integração em OMOP/CDM.
Linha de pesquisa
➡ Harmonização semântica (ETL → OMOP/CDM) com RAG + LLMs.
Relevância
⭐ Alta. Muito importante para seu trabalho com OMOP e pipelines ETL em saúde.

🔎 Artigo 14
Título: CD-Tron: Leveraging large clinical language model for early detection of cognitive decline from electronic health records
Fonte: Journal of Biomedical Informatics, 2025
Resumo (traduzido)
Apresenta o CD-Tron, um LLM clínico para detecção precoce de declínio cognitivo em EHRs. Usou 2.166 pacientes do Mass General Brigham, com notas clínicas dos 4 anos antes do diagnóstico de MCI. Modelo treinado em 4.949 seções rotuladas e avaliado em 1.996 notas. Incorporou SHAP para interpretabilidade.
Palavras-chave
Declínio cognitivo; Alzheimer; LLM clínico; EHR; interpretabilidade.
Metodologia
Dataset: EHR Mass General Brigham (2.166 pacientes).
Modelo: CD-Tron, baseado em LLM clínico.
Técnicas: SHAP para interpretabilidade; análise de erro.
Resultados
Alta sensibilidade, apenas 1 falso negativo.
Superou baselines em precisão, recall e AUC.
SHAP destacou termos-chave ligados ao declínio cognitivo.
Conclusões
CD-Tron identifica declínio cognitivo precoce em EHRs, com interpretabilidade integrada, útil para triagem clínica.
Linha de pesquisa
➡ Detecção precoce de Alzheimer/declínio cognitivo em EHRs com LLMs clínicos.
Relevância
⭐ Alta. Contribuição direta em EHR + doenças neurológicas.

🔎 Artigo 15
Título: A two-stage proactive dialogue generator for efficient clinical information collection using large language model
Fonte: Expert Systems with Applications, 2025
Resumo (traduzido)
Propõe um sistema de diálogo clínico proativo baseado em LLMs para coleta automática de informações diagnósticas. Estrutura em duas etapas: (1) geração de queries médicas relevantes, (2) ranking e refinamento das queries. Avaliado em dataset real de conversas médicas, mostrou desempenho superior a LLMs biomédicos e generalistas.
Palavras-chave
LLMs; diálogo médico; coleta de informações clínicas; diagnóstico assistido.
Metodologia
Arquitetura: dois estágios (geração + ranking).
Dataset real de diálogos médico-paciente.
Comparação com HuatuoGPT, Bio-Llama, Clinical-Deepseek.
Resultados
Melhor fluência, profissionalismo e segurança em comparação aos SOTA.
Capaz de simular estilo conversacional de médicos reais.
Coleta eficiente de dados diagnósticos em múltiplas rodadas.
Conclusões
LLMs podem automatizar entrevistas médicas estruturadas, otimizando coleta de dados e reduzindo fadiga do paciente.
Linha de pesquisa
➡ Sistemas de diálogo médico proativo (agente médico conversacional).
Relevância
⭐ Média-Alta. Útil como apoio para coleta de dados estruturados em EHRs.

🔎 Artigo 16
Título: Development and evaluation of a retrieval-augmented large language model framework for enhancing endodontic education
Fonte: International Journal of Medical Informatics, 2025
Resumo (traduzido)
O estudo desenvolveu o Endodontics-KB, uma plataforma multimodal (literatura + vídeos + imagens) integrada a um LLM com RAG, chamada EndoQ, para ensino em endodontia. Avaliou 11 questões validadas por especialistas, comparando o modelo com GPT-4, Qwen2.5 e DeepSeek R1. EndoQ obteve desempenho superior em acurácia clínica, relevância contextual, completude, profissionalismo e fluência linguística.
Palavras-chave
Educação em endodontia; LLMs; base multimodal de conhecimento; RAG.
Metodologia
Coleta de literatura, guidelines, slides, vídeos e imagens clínicas.
Construção do Endodontics-KB com reindexação semântica dinâmica.
Integração a EndoQ (LLM + RAG multimodal).
Avaliação cega por 2 especialistas usando escala Likert 5 pontos.
Resultados
EndoQ superou modelos generalistas em todas as métricas.
Acurácia: 4,45; Relevância: 4,59; Completude: 4,27; Profissionalismo: 4,45; Fluência: 4,86.
2.200 unidades de conhecimento integradas.
Conclusões
O framework melhora precisão e relevância do ensino clínico, podendo ser transferido para outras especialidades médicas.
Linha de pesquisa
➡ Educação médica com RAG + LLM multimodal.
Relevância
⭐ Média. Mais voltado à pedagogia clínica que ao ETL em saúde.

🔎 Artigo 17
Título: Empowering large language models for automated clinical assessment with generation-augmented retrieval and hierarchical chain-of-thought
Fonte: Artificial Intelligence in Medicine, 2025
Resumo (traduzido)
Apresenta o GAPrompt, um paradigma de prompting que combina: (i) seleção orientada de LLMs, (ii) construção de base de conhecimento aumentada por geração, (iii) summary-based GAR (SGAR), (iv) inferência com hierarchical chain-of-thought (HCoT) e (v) ensemble de gerações. Aplicado à avaliação quantitativa de gravidade do AVC (stroke severity) a partir de EHRs.
Palavras-chave
LLMs; avaliação clínica automatizada; GAR; HCoT; EHR.
Metodologia
Dataset: EHRs rotulados com escala NIHSS.
GAPrompt com LangChain para dividir documentos e aplicar CoT em dois níveis (macro e micro).
Ensemble para reduzir alucinações.
Resultados
Melhor precisão em avaliação quantitativa de AVC.
HCoT superou prompting linear.
SGAR melhorou recall e reduziu erros de contexto.
Conclusões
Paradigma viável para aplicar LLMs em avaliações clínicas quantitativas automatizadas.
Linha de pesquisa
➡ Avaliação quantitativa de AVC em EHR com prompting avançado (GAR + HCoT).
Relevância
⭐ Alta. Excelente exemplo de EHR + prompting estruturado.

🔎 Artigo 18
Título: Comparing the accuracy of large language models and prompt engineering in diagnosing real-world cases
Fonte: International Journal of Medical Informatics, 2025
Resumo (traduzido)
Estudo retrospectivo (1.122 registros hospitalares de reumatologia, China) comparou 4 LLMs (GPT-4o-mini, GPT-4o, ERNIE, Llama-3) em diagnóstico de doenças autoimunes (comuns e raras) e não reumáticas. Testou few-shot e chain-of-thought.
Palavras-chave
LLMs; engenharia de prompt; diagnóstico clínico; doenças reumáticas.
Metodologia
Base: registros hospitalares reais.
Modelos: GPT-4o, GPT-4o-mini, ERNIE, Llama-3.
Avaliação: acerto top-1 (hit1), top-2 e top-5.
Comparação de prompting zero-shot, few-shot e CoT.
Resultados
Acurácia (hit1): ERNIE 82,9%, Llama-3 82,7%, GPT-4o 82,4%, GPT-4o-mini 81,8%.
Few-shot melhorou GPT-4o para 85,9%.
CoT não trouxe ganhos significativos.
Diagnósticos reumáticos tiveram maior acurácia que não reumáticos.
Conclusões
LLMs são promissores no diagnóstico real, mas precisam de validação multicêntrica. O few-shot é eficaz, mas aumenta custo por diagnóstico.
Linha de pesquisa
➡ Diagnóstico automatizado em casos clínicos reais (reumatologia).
Relevância
⭐ Alta. Evidência prática do uso de LLMs em diagnóstico clínico direto.

🔎 Artigo 19
Título: Contrastive learning with large language models for medical code prediction
Fonte: Expert Systems with Applications, 2025
Resumo (traduzido)
Propõe o CL-LLM, framework que combina LLMs + aprendizado contrastivo para previsão automática de códigos ICD a partir de EHRs. Resolve os problemas de textos clínicos longos e de distribuição desbalanceada de rótulos (long-tailed). Testado em MIMIC-III, MIMIC-IV e MIMIC-III RARE50.
Palavras-chave
Classificação internacional de doenças; ICD; aprendizado contrastivo; LLMs.
Metodologia
Encoder: ClinicalBERT + segmentação de texto longo (split pooling).
LLM gera sinônimos de descrições de códigos para melhor representação.
Aprendizado contrastivo para capturar similaridade entre descrições.
Resultados
Superou SOTA em MIMIC-III e IV.
Melhor desempenho em rótulos raros (RARE50).
Validação robusta em múltiplos datasets.
Conclusões
LLMs + contraste aprimoram ICD coding automatizado, incluindo condições raras.
Linha de pesquisa
➡ Codificação automática ICD com aprendizado contrastivo + LLMs.
Relevância
⭐ Alta. Fundamental para padronização (ICD/OMOP) em ETL clínico.

🔎 Artigo 20
Título: Developing healthcare language model embedding spaces
Fonte: Artificial Intelligence in Medicine, 2024
Resumo (traduzido)
Explora pré-treinamento eficiente de LLMs compactos para aplicações em saúde. Testou três métodos: (i) Masked Language Modeling (MLM), (ii) Deep Contrastive Learning (DeCLUTR), e (iii) pré-treinamento baseado em metadados. Avaliou em MIMIC-III, Oxford Health NHS (OHFT) e NHS Patient Safety Incident Reports (PSIR).
Palavras-chave
LLMs; embeddings; contraste; saúde; classificação.
Metodologia
Adaptação de LLMs pequenos a dados clínicos.
Teste em tarefas de classificação (categoria de notas, triagem, severidade).
Comparação de embeddings gerados com diferentes técnicas.
Resultados
DeCLUTR (aprendizado contrastivo) teve melhor performance em todas as bases.
Embeddings com metadados mostraram maior separabilidade de clusters.
Todos os LLMs adaptados superaram seus modelos base generalistas.
Conclusões
Adaptação com pré-treinamento especializado é viável mesmo em ambientes com poucos recursos, garantindo uso ético e eficiente em saúde.
Linha de pesquisa
➡ Embeddings clínicos otimizados para classificação e triagem.
Relevância
⭐ Média-Alta. Boa base para embedding clínico local (EHRs sensíveis/NHS).

🔎 Artigo 21
Título: Enhancing data quality in medical concept normalization through large language models
Fonte: University of North Texas & North Carolina State University, 2025
Resumo (traduzido)
O estudo investiga como melhorar a qualidade dos dados em normalização de conceitos médicos (MCN) usando LLMs, particularmente ChatGPT. A MCN é crítica para mapear termos informais a conceitos médicos padronizados, mas muitos estudos ignoram o papel da qualidade dos dados. O trabalho avalia diferentes cenários de qualidade, aplicando técnicas de zero-shot e few-shot prompting para data augmentation e examinando como isso impacta o desempenho dos modelos de MCN.
Palavras-chave
Normalização de conceitos médicos; Aprendizado de máquina; Qualidade de dados; Data augmentation; LLM; ChatGPT.
Metodologia
Avaliação de qualidade de dataset de MCN.
Geração de dados sintéticos com ChatGPT zero-shot prompting.
Medição da qualidade (correção e abrangência).
Experimentos para medir impacto de duplicação, vieses e distribuição dos dados.
Aplicação de few-shot prompting com contexto para aprimorar desempenho.
Resultados
Dados duplicados distorcem avaliação de modelos.
Zero-shot tende a gerar duplicatas em regiões médias da distribuição.
É necessário incluir os dados aumentados também no testing set para avaliar eficácia.
Few-shot com contexto + exemplos representativos → melhora significativa da qualidade e do modelo.
Conclusões
LLMs podem gerar dados úteis para MCN, mas o sucesso depende da estratégia de prompting. O framework proposto oferece diretrizes para avaliar e melhorar qualidade de dados em deep learning biomédico.
Linha de pesquisa
➡ Qualidade de dados + Data augmentation em MCN com LLMs.
Relevância
⭐ Alta. Central para ETL clínico e interoperabilidade OMOP/SNOMED.

🔎 Artigo 22
Título: Enhancing early detection of cognitive decline in the elderly: a comparative study utilizing large language models in clinical notes
Fonte: eBioMedicine – The Lancet, 2024
Resumo (traduzido)
Avalia LLMs (Llama 2, GPT-4) na detecção de declínio cognitivo em anotações de EHRs. Comparou com redes neurais hierárquicas e XGBoost, além de criar ensemble models. O objetivo foi verificar erros, complementaridade e eficácia em diagnóstico precoce de Alzheimer/demência.
Palavras-chave
Disfunção cognitiva; NLP; EHRs; Alzheimer; Demência; Diagnóstico precoce.
Metodologia
Local: Mass General Brigham, Boston.
Dados: 6945 notas clínicas anotadas (4 anos antes do diagnóstico de MCI).
Modelos: Llama 2, GPT-4, baseline neural attention, XGBoost.
Estratégias: hard prompting, RAG, prompts baseados em análise de erros.
Ensemble por majority voting.
Resultados
GPT-4 > Llama 2 em acurácia, mas não superou modelos locais.
Ensemble (LLMs + ML local) → melhor resultado: precisão 90,2%, recall 94,2%, F1 92,1%.
Erros diversificados entre modelos; só 3,2% dos casos foram erro comum a todos.
Conclusões
LLMs generalistas ainda não substituem modelos locais treinados com EHR, mas em conjunto são complementares e melhoram performance.
Linha de pesquisa
➡ Diagnóstico precoce de Alzheimer/demência em EHR com ensemble LLM + ML local.
Relevância
⭐ Muito alta. Mostra aplicação clínica direta em notas reais de PEP.

🔎 Artigo 23
Título: Exploring a learning-to-rank approach to enhance the RAG-based electronic medical records search engines
Fonte: Vanderbilt University Medical Center, 2025
Resumo (traduzido)
Propõe um método de learning-to-rank para personalizar busca em RAG aplicado a EHRs, adaptando-se à semântica do usuário para melhorar relevância e precisão em recuperação de informações médicas.
Palavras-chave
RAG; EHRs; Recuperação de informação; LLMs; Learning-to-rank.
Metodologia
Usuário fornece rótulos de relevância em alguns docs.
Sistema ajusta embeddings semânticos médicos.
RAG consulta apenas top documentos rerankeados.
Avaliação: 4 médicos especialistas testaram em cenários clínicos.
Resultados
Obteve P@10 ≥ 0.60 com apenas 10 rótulos positivos.
Transfer learning bem-sucedido em novos datasets.
Melhora em detecção de progressão de câncer: de 0.14 → 0.50.
Conclusões
Learning-to-rank + RAG adapta busca a preferências do médico, produzindo respostas mais significativas.
Linha de pesquisa
➡ Customização semântica em RAG para busca clínica.
Relevância
⭐ Alta. Essencial para sistemas de busca médicos baseados em LLMs.

🔎 Artigo 24
Título: Few-shot medical relation extraction via prompt tuning enhanced pre-trained language model
Fonte: Neurocomputing, 2025
Resumo (traduzido)
Apresenta o FSRE (Few-shot Relation Extraction) com prompt tuning para extrair relações médicas de EHRs com poucos dados anotados. Avaliado nos datasets i2b2/VA, CHEMPROT e BioRED.
Palavras-chave
Extração de relações; Few-shot learning; Prompt tuning; EHRs.
Metodologia
Base: BERT com hard prompts (“The relation between them is”).
Prototypical networks para calcular distâncias em embeddings.
Avaliado em 3 corpora biomédicos.
Resultados
Superou modelos SOTA em 3-way-5-shot.
Ganhos de até +20,2% no CHEMPROT e +10,9% no BioRED.
Melhor generalização para relações não vistas.
Conclusões
Método eficaz para lidar com escassez de dados anotados e extrair relações clínicas de forma robusta.
Linha de pesquisa
➡ Relação médico-conceito em EHRs com few-shot + prompts.
Relevância
⭐ Muito alta. Impacto direto em Knowledge Graphs clínicos/OMOP.

🔎 Artigo 25
Título: Fine-tuning language model embeddings to reveal domain knowledge: An explainable AI perspective on medical decision making
Fonte: Engineering Applications of AI, 2025 (Medical University of Graz)
Resumo (traduzido)
Investiga fine-tuning de BERT em alemão aplicado a relatórios de patologia (1,28M documentos). Foco em explicabilidade (XAI) e uso de embeddings para estruturar decisões médicas.
Palavras-chave
Patologia; LLM; BERT; Alemão; Fine-tuning; Embeddings; XAI.
Metodologia
Dados: 641k pacientes, 23 anos de relatórios.
Modelos: BERT-base-German vs. BERT adaptado (“BERT-patho”).
Tarefas: classificação binária (tumor/não) e multiclasses (comportamento tumoral).
Avaliação: Acurácia, F1, AUC + análise de embeddings (t-SNE, DBSCAN, LDA).
Resultados
Acurácia ~98% nas duas tarefas.
Embeddings exibiram linear separability até em dimensões individuais.
“Score & vote model” de neurônios confiáveis → transparência nas decisões.
Clusters revelaram padrões médicos alinhados à opinião de patologistas.
Conclusões
Fine-tuning em relatórios patológicos gera embeddings explicáveis e úteis para decisão médica interpretável, além de abrir caminho para integração multimodal (texto + imagem).
Linha de pesquisa
➡ XAI em NLP clínico (patologia, relatórios EHR).
Relevância
⭐ Média-alta. Mais metodológico, mas crucial para explicabilidade e confiança.

🔎 Artigo 26
Título: From BERT to generative AI – Comparing encoder-only vs. large language models in a cohort of lung cancer patients for named entity recognition in unstructured medical reports
Fonte: Computers in Biology and Medicine, 2025
Resumo (traduzido)
O estudo comparou modelos encoder-only (BERT e variantes) e LLMs gerativos (instruction-tuned) em NER clínico aplicado a relatórios de patologia e radiologia de pacientes com câncer de pulmão. Foram testadas três abordagens: NER plano, NER aninhado e NER baseado em instruções (LLMs).
Resultados principais:
Modelos encoder obtiveram os melhores F1 (0,87–0,88 em patologia; até 0,78 em radiologia).
LLMs, embora de alta precisão, tiveram recall muito baixo (F1 = 0,18–0,30).
LLMs mostraram comportamento conservador, extraindo menos entidades, mas mais precisas.
Palavras-chave
Machine learning; NLP; LLMs; Extração de entidades médicas; NER clínico.
Metodologia
Dados: 2.426 relatórios (2.013 patologia; 413 radiologia) de 523 pacientes (Essen, Alemanha).
Anotação: equipe de estudantes de medicina + validação por oncologistas.
Modelos: gBERT, GBERT-BioM, medBERT.de, mBERT vs. LLMs como SauerkrautLM-Nemo-12B-Instruct, Llama-3.1-8B-Instruct, Phi-3.5-mini.
Avaliação: F1 estrito (token/span) para encoders; reconhecimento de tipo para LLMs.
Resultados
Encoders especializados em biomédico > LLMs generalistas.
LLMs falharam em entidades múltiplas/aninhadas.
Custo computacional dos LLMs não compensou ganhos.
Conclusões
LLMs ainda não são adequados para NER clínico abrangente, sendo os encoders biomédicos a melhor escolha no cenário atual.
Linha de pesquisa
➡ Comparação encoder vs. LLM em NER clínico real (oncologia, DE).
Relevância
⭐ Muito alta. Evidência direta para extração de entidades biomédicas em PEPs.

🔎 Artigo 27
Título: From text to data: Open-source large language models in extracting cancer-related medical attributes from German pathology reports
Fonte: International Journal of Medical Informatics, 2025
Resumo (traduzido)
Investigou o uso de LLMs open-source (Llama, Mistral, SauerkrautLM) em extração de atributos oncológicos de relatórios patológicos em alemão, considerando restrições de privacidade (GDPR). Desenvolveu pipeline RAG + prompting (zero/few-shot) em ambiente on-premise.
Resultados principais:
Llama 3.3 70B teve melhor performance geral (F1 > 0,90).
Mistral Small 24B + RAG atingiu resultados equivalentes, com menor custo computacional.
RAG ajudou especialmente em atributos raros (metástase, estadiamento UICC).
Palavras-chave
Oncologia; LLMs open-source; Patologia; RAG; Prompting; Privacidade; Extração de atributos.
Metodologia
Dataset: 522 relatórios anotados manualmente (Gold Dataset) + 15.000 relatórios para RAG.
Modelos testados: Llama 70B, Mistral 24B, SauerkrautLM (8B, 12B).
Estratégias: zero-shot, few-shot, RAG+few-shot.
Avaliação: precisão, recall, acurácia, F1 macro.
Resultados
Llama 70B > 0.90 em todas métricas.
Mistral 24B + RAG ≈ Llama 70B, sem diferença estatística (p > 0,05).
Pequenos modelos (Llama 8B) melhoraram até +20% com RAG.
Conclusões
LLMs open-source + RAG permitem extração oncológica precisa, eficiente e em conformidade com privacidade, reduzindo dependência de modelos proprietários.
Linha de pesquisa
➡ Extração de atributos oncológicos em patologia com RAG e LLMs open-source.
Relevância
⭐ Muito alta. Combina interoperabilidade + privacidade + eficiência.

🔎 Artigo 29
Título: Integrating large language models with human expertise for disease detection in electronic health records
Fonte: Computers in Biology and Medicine, 2025 – University of Calgary (Canadá)
Resumo (traduzido)
Este estudo propõe um pipeline baseado em LLMs generativos integrado com conhecimento de especialistas clínicos para detecção de múltiplas doenças a partir de notas clínicas em EHRs. O sistema foi aplicado para identificar infarto agudo do miocárdio (IAM), diabetes e hipertensão, comparando os resultados com diagnósticos validados por clínicos e métodos baseados em códigos ICD-10.
Coorte: 3.088 pacientes (551.095 notas clínicas, Alberta, 2015).
Prevalência: IAM 55,4%, Diabetes 27,7%, Hipertensão 65,9%.
Resultados:
IAM: sensibilidade 88%, especificidade 63%, PPV 77%.
Diabetes: sensibilidade 91%, especificidade 86%, PPV 71%.
Hipertensão: sensibilidade 94%, especificidade 32%, PPV 72%.
O método LLM superou ICD-10 em sensibilidade e NPV, mas teve menor especificidade.
As curvas mensais dos casos detectados acompanharam as tendências reais de diagnóstico.
Palavras-chave
Processamento de linguagem natural; Fenotipagem de doenças; Vigilância em saúde pública; Epidemiologia; Doenças cardiovasculares.
Metodologia
Fonte: base CREATE (APPROACH Registry + EHR + bases administrativas).
Pipeline:
Pré-processamento com prompts para filtrar tipos de documentos relevantes.
Prompts de inferência para cada doença (diagnóstico direto e/ou raciocínio inferencial).
Inferência via Mistral-7B-OpenOrca (em ambiente seguro e on-premise).
Pós-processamento com regras clínicas (ex.: troponina, pressão arterial, glicemia).
Comparação: contra diagnósticos clínicos validados + ICD-10.
Resultados
Alta sensibilidade, especialmente para hipertensão (94%) e diabetes (91%).
Especificidade mais baixa que ICD-10, mas maior capacidade de identificar casos suspeitos.
Transparência: LLM destacou frases de suporte para suas decisões (ex.: valores de troponina para IAM).
Combinação LLM + ICD-10 → maior sensibilidade (IAM 95%, Diabetes 94%, Hipertensão 96%), mas queda de especificidade.
Conclusões
O pipeline LLM + expertise clínica mostrou eficiência e escalabilidade na detecção de doenças sem necessidade de anotações manuais.
Útil para vigilância em tempo real, mesmo com limitações de falsos positivos.
Estrutura transparente e adaptável, executável dentro de firewalls hospitalares → relevante para privacidade.
Linha de pesquisa
➡ LLMs + especialistas para fenotipagem de múltiplas doenças em EHRs (cardiologia, crônicas).
Relevância
⭐ Muito alta. Impacto direto em vigilância epidemiológica, fenotipagem automatizada e integração em saúde pública.

🔎 Artigo 30
Título: Intelligent Chinese patent medicine (CPM) recommendation framework: Integrating large language models, retrieval-augmented generation, and the largest CPM dataset
Fonte: Pharmacological Research, 2025 – China Academy of Chinese Medical Sciences
Resumo (traduzido)
Este estudo desenvolveu o RAG-CPMF, um sistema inteligente de recomendação de medicamentos patenteados chineses (CPMs) que integra LLMs, RAG e o maior dataset de CPM já construído (8.644 registros).
Usou técnica Multi-LLMs Validation para extrair informações estruturadas de bulas e instruções oficiais, reduzindo necessidade de revisão manual.
Dataset inclui: composição, indicações, contraindicações, efeitos adversos, farmacologia.
Avaliado contra diretrizes clínicas nacionais de TCM.
Resultados mostraram que o RAG-CPMF superou LLMs generalistas em precisão de recomendações.
Palavras-chave
LLMs; RAG; Medicina Tradicional Chinesa; Dataset; Recomendação inteligente; Validação multi-LLMs; Diretrizes clínicas.
Metodologia
Fonte: NMPA (National Medical Products Administration) + bulas oficiais.
Dataset final: 8.644 CPMs únicos, com campos estruturados (indicações, dosagem, efeitos adversos, farmacologia etc.).
Técnicas:
Multi-LLMs Validation → 5 modelos (Qwen, GLM, ERNIE, Llama, ChatGPT).
Prompting avançado: CoT, ToT, FoT (Forest of Thought).
RAG para vincular sintomas a recomendações.
Avaliação:
Guidelines evaluation (precisão vs. diretrizes TCM).
Clinician evaluation (SUS) – segurança, usabilidade, fluidez.
Resultados
Forest of Thought (FoT) foi o melhor prompting em extração de informações estruturadas.
Multi-LLMs Validation obteve 100% de precisão quando ≥4 modelos concordavam.
RAG-CPMF melhorou significativamente a consistência com diretrizes clínicas, superando LLMs generalistas (~60% de consistência).
Médicos relataram que o sistema aumenta a segurança e confiança clínica.
Conclusões
RAG-CPMF é um framework robusto, validado clinicamente e alinhado a diretrizes, que pode apoiar médicos ocidentais no uso seguro de CPMs.
Demonstra como datasets bem estruturados + RAG + LLMs especializados podem reduzir alucinações e gerar recomendações confiáveis.
Linha de pesquisa
➡ LLMs + RAG em farmacologia/TMC, recomendação terapêutica orientada a sintomas.
Relevância
⭐ Média-alta. Não é diretamente sobre PEPs, mas é forte evidência de RAG + LLMs em recomendações clínicas seguras.


🔎 Artigo 32
Título: Large Language Models Applied to Health Care Tasks May Improve Clinical Efficiency, Value of Care Rendered, Research, and Medical Education
Fonte: Arthroscopy, 2024
Resumo (traduzido)
Analisa como LLMs (ChatGPT, Gemini, LLaMA, Mistral, etc.) podem ser aplicados em atenção à saúde, pesquisa e educação médica.
Áreas de impacto: atendimento clínico, fluxo de trabalho, operações hospitalares, comunicação, educação, pesquisa.
Técnicas de personalização: prompt engineering, RAG, fine-tuning, agentic augmentation.
Potenciais: triagem, documentação automatizada, tradução, tutoria interativa, revisão científica, auxílio em autorização médica.
Alertas: riscos de vieses, alucinações, desinformação e questões éticas.
Palavras-chave
LLMs; Cuidados de saúde; Educação médica; Workflow; Pesquisa clínica; RAG.
Metodologia
Revisão narrativa (nível de evidência V, opinião especializada).
Discussão de casos (documentação, triagem, voice-to-text, ensino médico).
Comparação entre LLMs off-the-shelf e modelos customizados.
Resultados
LLMs generalistas têm ~30% de erros em conteúdo médico.
Aplicação de RAG em guidelines aumentou acurácia em +39,7%.
Chatbots foram avaliados como mais empáticos e claros que médicos em 78,6% das interações sociais analisadas.
Custom LLMs oferecem maior controle, transparência e confiabilidade.
Conclusões
LLMs podem melhorar eficiência clínica, educação e pesquisa, mas precisam de customização + validação clínica para garantir segurança.
Linha de pesquisa
➡ Aplicações clínicas, administrativas e educacionais de LLMs na saúde.
Relevância
⭐ Alta. Fundamenta cenários de uso amplo de LLMs em saúde (clínico, educacional, pesquisa).

🔎 Artigo 33
Título: Large Language Models, scientific knowledge and factuality: A framework to streamline human expert evaluation
Fonte: Journal of Biomedical Informatics, 2024
Resumo (traduzido)
Propõe um framework de avaliação manual assistida para medir a factualidade de LLMs em biomedicina, especialmente em descoberta de antibióticos.
Problema: LLMs têm fluência, mas baixa factualidade, viés para entidades mais frequentes e risco de alucinação.
Framework: divide a avaliação em 3 etapas → fluência, alinhamento de prompt, coerência semântica, factualidade, especificidade.
Reduz esforço de especialistas, distribuindo parte da análise a não-experts.
Palavras-chave
Factualidade; LLMs; RAG; Antibiotic discovery; Avaliação humana.
Metodologia
Avaliou 11 LLMs (GPT-2/3/4, Llama 2, Bloom, BioGPT etc.).
Tarefas: (1) geração de definição de compostos químicos, (2) identificação de relações químico-fungo.
Avaliação qualitativa por especialistas.
Resultados
Modelos recentes melhoraram fluência, mas ainda falham em factualidade.
Viés forte para entidades comuns (co-ocorrência).
LLMs não são adequados como bases de conhecimento biomédico factual em zero-shot.
Potencial aumenta com especialização de domínio + feedback humano.
Conclusões
LLMs não podem substituir especialistas em conhecimento factual biomédico, mas frameworks como este ajudam a tornar a avaliação mais sistemática e escalável.
Linha de pesquisa
➡ Factualidade e avaliação de conhecimento científico em LLMs biomédicos.
Relevância
⭐ Muito alta. Crítico para discutir confiabilidade de LLMs em ciência e saúde.

🔎 Artigo 34
Título: The application of large language models in medicine: A scoping review
Fonte: iScience, 2024 – Xiangbin Meng et al.
Resumo (traduzido)
Esta revisão sistemática examinou 550 estudos sobre a aplicação de LLMs em medicina. Os resultados mostraram que LLMs como ChatGPT transformaram práticas médicas em áreas como diagnóstico, redação científica, educação médica e gestão de projetos.
Usos frequentes: geração de documentos médicos, simulações de treinamento, otimização de revisão de literatura, apoio em diagnóstico auxiliar e comunicação médico-paciente.
Limitações: compreensão contextual insuficiente, risco de dependência excessiva e alucinações.
Tendências: crescimento acelerado das publicações entre 2021 e 2023, com forte presença nos EUA, Índia e China.
Futuros caminhos: LLMs multimodais, integração ética, validação clínica e uso responsável.
Palavras-chave
LLMs; Medicina; Diagnóstico assistido; Educação médica; Escrita científica; Multimodalidade; Ética em IA.
Metodologia
Bases consultadas: PubMed, Embase, Scopus, Web of Science.
Período: até julho/2023.
Identificados: 4.323 artigos → após triagem e exclusões → 550 incluídos.
Análise: categorização por área (diagnóstico, educação, escrita, comunicação).
Avaliação geográfica e por sistemas orgânicos (urológico, circulatório, digestivo, nervoso, etc.).
Resultados
Crescimento abrupto: de 1 artigo em 2021 para 126 em maio/2023.
Áreas mais exploradas: diagnóstico auxiliar, comunicação médico-paciente, escrita científica.
EUA (221 publicações) lideram, seguidos de Índia (53) e China (49).
Uso em sistemas específicos: urinário (37 estudos), circulatório e digestivo (27 cada).
Aplicações emergentes em educação médica, enfermagem e apoio psicossocial.
Multimodal LLMs (texto + imagem + som) já começam a integrar radiologia, genômica e biomarcadores.
Conclusões
LLMs estão revolucionando a medicina, mas ainda exigem:
Validação clínica robusta em cenários reais.
Protocolos éticos de privacidade e transparência.
Complementariedade com médicos, e não substituição.
Foco em multimodalidade e explicabilidade.
Linha de pesquisa
➡ Panorama global sobre aplicações de LLMs em saúde, incluindo multimodalidade, diagnóstico, escrita médica, educação e comunicação.
Relevância
⭐ Muito alta. É um artigo de referência (estado da arte) que pode embasar a introdução e revisão teórica do seu artigo.

🔎 Artigo 35
Título: Large language models for clinical decision support in gastroenterology and hepatology
Fonte: Nature Reviews Gastroenterology & Hepatology, 2025 – Wiest et al.
Resumo (traduzido)
Este artigo explora como LLMs podem transformar sistemas de apoio à decisão clínica (CDSS) em gastroenterologia e hepatologia, áreas com crescente complexidade devido a doenças como doença hepática gordurosa associada à disfunção metabólica (MASLD), hepatocarcinoma (HCC), câncer colorretal (CRC) e doenças inflamatórias intestinais (IBD).
Problema: CDSS tradicionais (baseados em regras/ML) enfrentam baixa adoção clínica.
Solução: LLMs multimodais + RAG + agentes oferecem suporte dinâmico e contextualizado, integrando dados de EHR, guidelines e literatura científica.
Potenciais: recomendações personalizadas, análise de exames, integração multimodal (texto, imagem, genômica).
Desafios: vieses, alucinações, interoperabilidade e falta de validação clínica.
Palavras-chave
CDSS; Gastroenterologia; Hepatologia; LLMs; RAG; Multimodalidade; Apoio à decisão.
Metodologia
Revisão narrativa com foco em complexidade clínica vs. evolução tecnológica dos CDSS.
Comparação histórica: anos 1970 (rule-based) → 1990s (EHRs + interoperabilidade) → 2010s (ML, Watson) → 2020s (LLMs + RAG + agentes).
Exemplos clínicos analisados:
IBD → necessidade de gestão personalizada + suporte psicossocial.
MASLD → alta prevalência global, novos tratamentos (GLP1), estratificação complexa.
HCC → decisões influenciadas por múltiplos scores (Child-Pugh, MELD, BCLC).
CRC → necessidade de rastreamento personalizado (ctDNA, colonoscopia).
Resultados
Estudos iniciais mostram desempenho promissor, mas ainda limitado:
ChatGPT-3.5 em oncologia → acurácia 56,1%.
Copilot e GPT em cenários de pâncreas → apenas 44% de aderência a guidelines.
Chatbots para câncer colorretal → alguns superaram médicos residentes em respostas factuais.
GastroBot (LLM RAG + fine-tuning) → recall 95%, relevância 92,28%, fidelidade 93,73%.
Fine-tuning de BioMistral7B → melhor interpretação de sorologia de hepatite.
Conclusões
LLMs oferecem novo paradigma em CDSS, mas ainda não há aprovação regulatória (FDA).
Integração deve focar em segurança, confiabilidade, personalização e supervisão médica.
O futuro aponta para CDSS baseados em agentes multimodais, com capacidade de dialogar, corrigir vieses e integrar dados heterogêneos.
Linha de pesquisa
➡ LLMs como núcleo de CDSS em gastroenterologia/hepatologia (doenças complexas, multimodalidade e integração RAG).
Relevância
⭐ Muito alta. É um estudo de referência especializado em uma das áreas mais desafiadoras para apoio à decisão clínica.

🔎 Artigo 36
Título: Adapted large language models can outperform medical experts in clinical text summarization
Fonte: Nature Medicine, 2024 – Van Veen et al.
Resumo (traduzido)
Este estudo investigou o desempenho de LLMs adaptados em tarefas de sumarização clínica (radiologia, perguntas de pacientes, notas de progresso e diálogos médico-paciente). Foram avaliados oito LLMs (GPT-3.5, GPT-4, FLAN-T5, UL2, Vicuna, Llama-2, Alpaca e Med-Alpaca), adaptados via in-context learning (ICL) e QLoRA.
Achado central: o GPT-4 (com ICL) superou médicos especialistas em critérios de completude, correção e concisão.
Estudo clínico leitor: 10 médicos avaliaram as saídas → em 81% dos casos, os resumos do modelo foram iguais ou melhores que os humanos (45% equivalentes, 36% superiores).
Segurança: análises mostraram que médicos também cometem “alucinações” (erros factuais), não apenas os modelos.
Impacto: indica que LLMs adaptados podem reduzir o peso da documentação clínica e liberar tempo para o cuidado direto ao paciente.
Palavras-chave
LLMs; Sumarização clínica; QLoRA; In-context learning; GPT-4; Radiologia; Notas de progresso; Segurança clínica.
Metodologia
Modelos testados: 6 open-source + 2 proprietários (GPT-3.5 e GPT-4).
Tarefas: 4 tipos de sumarização clínica (radiologia, progress notes, perguntas de pacientes e diálogos).
Adaptação: In-context learning (com exemplos) e QLoRA (fine-tuning leve).
Avaliação:
Quantitativa → métricas sintáticas, semânticas e conceituais (BLEU, MEDCON).
Qualitativa → estudo cego com 10 médicos (5 hospitalistas, 5 radiologistas).
Segurança → análise de potenciais danos clínicos ligados a erros de LLMs vs. médicos.
Resultados
Melhor modelo: GPT-4 (32k contexto, ICL).
Desempenho:
Mais completo e correto que médicos em 3 das 4 tarefas (p < 0.001).
Resumos de médicos preferidos em apenas 19% dos casos.
Menos erros factuais que humanos em radiologia e progress notes.
Segurança: risco de dano potencial menor com GPT-4 (16%) do que com médicos (22%).
Erro humano: médicos também produziram “alucinações” (ex.: diagnóstico incorreto).
Conclusões
LLMs adaptados podem superar médicos em tarefas de sumarização clínica.
Implicações diretas para redução de burnout médico e melhora da qualidade da documentação.
Mas ainda há riscos → integração deve ser gradual, com monitoramento e protocolos de segurança.
Linha de pesquisa
➡ Avaliação experimental da superioridade de LLMs adaptados sobre médicos em tarefas de sumarização clínica.
Relevância
⭐ Muito alta → traz evidência direta de que LLMs podem superar especialistas em tarefas práticas, sendo referência obrigatória no debate sobre eficiência, segurança e adoção clínica.

🔎 Artigo 37
Título: Scalable incident detection via natural language processing and probabilistic language models
Fonte: Scientific Reports, 2024 – Walsh et al.
Resumo (traduzido)
O artigo apresenta um método de detecção de incidentes clínicos em larga escala (ex.: tentativas de suicídio, comportamentos relacionados ao sono) usando NLP com modelos probabilísticos aplicados a textos não estruturados de EHRs. O sistema busca identificar eventos incidentes (novos) e não apenas prevalência. Validado em 89.428 registros de tentativas de suicídio e 35.863 de distúrbios do sono, o modelo mostrou boa performance para suicídio (AUPR ~ 0.77) e desempenho mais modesto para sono (AUPR ~ 0.31).
O trabalho reforça a importância de usar textos clínicos não estruturados para vigilância pós-mercado de medicamentos e saúde populacional, mas alerta para a necessidade de algoritmovigilância e mitigação de vieses raciais.
Palavras-chave
NLP; Detecção de incidentes; EHR; Fenotipagem; Suicídio; Distúrbios do sono; Vigilância pós-mercado.
Metodologia
Fonte de dados: Vanderbilt University Medical Center (1998–2022).
Fenótipos: tentativas de suicídio e distúrbios relacionados ao sono (ex.: sonambulismo).
Processo:
NLP com embeddings (Word2Vec + BERT) para expansão de termos.
“Phenotype Retrieval” (PheRe) → atribui score contínuo (TF-IDF + cosine similarity).
Silver standard: códigos ICD.
Gold standard: revisão manual de prontuários.
Validação: métricas AUROC, AUPR, precisão, recall, F1-score.
Resultados
Suicídio: AUPR ~ 0.77 (95% CI 0.75–0.78).
Sono: AUPR ~ 0.31 (95% CI 0.28–0.34).
Threshold ótimo (F1-score):
Suicídio → 25 (Recall 0.93; Precision 0.63).
Sono → 35 (Recall 0.57; Precision 0.33).
Diferenças de desempenho por raça → impacto de vieses, exigindo “algoritmovigilância”.
Conclusões
NLP pode detectar incidentes clínicos raros de forma escalável, mas desempenho varia por fenótipo.
Necessário gold standard humano para validações robustas.
Promissor para FDA Sentinel e vigilância de eventos adversos pós-comercialização de fármacos.
Viés racial identificado → recomenda monitoramento contínuo.
Linha de pesquisa
➡ Uso de NLP probabilístico para detecção de incidentes em saúde (suicídio, sono), com foco em vigilância populacional e farmacovigilância.
Relevância
⭐ Alta → estudo pioneiro em detecção de incidentes clínicos em larga escala, relevante para temas de segurança, farmacovigilância e saúde mental.

🔎 Artigo 38
Título: Dual retrieving and ranking medical large language model with retrieval-augmented generation
Fonte: Scientific Reports, 2025 – Yang et al.
Resumo (traduzido)
Este estudo apresenta um framework de RAG em duas etapas (dual retrieval + dual ranking) para LLMs médicos, visando resolver os principais desafios de precisão e responsividade em tempo real. O sistema integra:
Elasticsearch (busca lexical),
Chroma (busca vetorial semântica),
ColBERTv2 (re-ranking semântico).
A base de conhecimento foi construída a partir de documentos médicos revisados por especialistas, continuamente atualizada. O modelo utilizado foi o IvyGPT, adaptado para saúde.
Resultados mostraram ganho de 10% em acurácia em consultas médicas complexas, comparado a LLMs standalone e variantes RAG simples. O estudo ressalta que a latência ainda é um problema em emergências, mas pode ser mitigada com hardware mais robusto.
Palavras-chave
LLM médico; RAG; Recuperação dual; IvyGPT; Elasticsearch; Chroma; ColBERTv2.
Metodologia
Modelo-base: IvyGPT (LLaMA adaptado + QLoRA + RLHF).
Arquitetura:
Consulta → representada em vetores.
Recuperação híbrida: Elasticsearch (palavra-chave) + Chroma (semântica).
Re-ranking via ColBERTv2.
IvyGPT gera resposta final + módulo de Check compara com fontes recuperadas.
Avaliação:
Experimentos de ablação (Chroma, Elasticsearch, ColBERTv2 removidos).
Comparação com sistemas RAG clássicos e SELF-RAG.
Validação por 100 avaliadores (médicos, estudantes, professores) em métricas: Relevância, Acurácia, Velocidade, Utilidade, Antropomorfismo.
Resultados
Melhor sistema (IvyGPT + Chroma + Elasticsearch + ColBERTv2):
MRR 0.72 | MAP 0.63, superior ao SELF-RAG (MRR 0.69 | MAP 0.60).
Acurácia +10% em consultas médicas complexas.
Melhor em Relevância e Utilidade, menor velocidade (trade-off).
Capaz de guiar pacientes a departamentos corretos em casos simulados.
Qualidade cai quando a base inclui dados não revisados (ex.: fóruns médicos → aumenta ruído).
Conclusões
O framework híbrido reduz alucinações e aumenta interpretabilidade.
Base de dados curada por médicos é crítica para resultados confiáveis.
Limitações: latência e dependência de dados de alta qualidade.
Futuro: otimizações com hardware acelerado, neural rankers mais leves, pruning inteligente.
Linha de pesquisa
➡ Avanço em RAG médico (dual retrieval + dual ranking) com IvyGPT, visando precisão, interpretabilidade e aplicação prática em triagem e suporte clínico.
Relevância
⭐ Muito alta → referência fundamental para mecanismos avançados de RAG em saúde.

🔎 Artigo 39
Título: A large language model based pipeline for extracting information from patient complaint and anamnesis in clinical notes for severity assessment
Fonte: Scientific Reports, 2025 – Gao et al.
Resumo (traduzido)
Este estudo propõe um pipeline baseado em LLMs para avaliar gravidade de pacientes em pronto-socorro (ED) a partir de queixa principal + anamnese em notas clínicas.
O pipeline combina:
Prompt estruturado (múltipla escolha A/B),
In-context learning (ICL),
RAG (similaridade textual com K exemplos próximos).
Três LLMs foram avaliados (GLM-4, ChatGLM-2, Alpaca-2) em 27.187 registros do banco CETAT (China, 2021).
A pontuação de severidade (xtext) foi incorporada em modelos de regressão logística, elevando a AUC-ROC de 0.746 para 0.802 na predição de pacientes críticos na triagem inicial (sem sinais vitais ainda).
Palavras-chave
LLM; Pronto-socorro; Triagem; Queixa clínica; Anamnese; RAG; ICL; Previsão de gravidade.
Metodologia
Dados: CETAT (27.187 pacientes, 2 hospitais). Inclui demografia, sinais vitais, consciência, anamnese, desfecho (CPR, óbito em ED) e triagem nível 1–4.
Pipeline:
Input = queixa/anamnese.
Prompt múltipla escolha (A = grave, B = não grave).
ICL (K=3 exemplos semelhantes da base via embeddings piccolo-large-zh).
RAG → traz exemplos relevantes para robustecer output.
Modelos avaliados: GLM-4, ChatGLM-2, Alpaca-2, + comparação com JINA-v2/v3 (BERT).
Predição: Regressão logística (Modelbase = estruturado; Modeltext = estruturado + xtext).
Validação: leave-one-out (LOO) + split train/test.
Resultados
Desempenho (GLM-4 + Alpaca-2):
AUC-ROC inicial: 0.746 → 0.802 (fase sem sinais vitais).
Ganhos menores após sinais vitais + GCS disponíveis (fase 2b: ganhos ~0.005–0.051).
Recall muito alto (GLM-4: 0.962) → bom para sensibilidade inicial, ainda que com baixa precisão.
Shapley values: xtext teve impacto maior que sinais vitais como temperatura, SpO₂ e pressão arterial diastólica.
Confusão: GLM-4 → alta sensibilidade (overcall); Alpaca-2 → maior equilíbrio precisão/recall.
Conclusões
LLMs conseguem extrair valor preditivo relevante de anamnese antes de sinais vitais, útil para triagem inicial em ED.
Ganhos diminuem à medida que mais variáveis estruturadas ficam disponíveis.
Integração ótima: usar LLMs para enriquecer dados não estruturados + modelos estatísticos para decisão.
Futuro: fine-tuning médico específico pode ampliar ganhos.
Linha de pesquisa
➡ Uso de LLMs com ICL+RAG para triagem precoce em emergências a partir de texto livre de anamnese.
Relevância
⭐ Muito alta → estudo inovador e aplicado diretamente à triagem hospitalar, extremamente pertinente ao tema de NLP clínico + decisão crítica em tempo real.

🔎 Artigo 40
Título: A scalable framework for evaluating multiple language models through cross-domain generation and hallucination detection
Fonte: Scientific Reports, 2025 – Chakraborty et al.
Resumo (traduzido)
Este trabalho apresenta o MultiLLM-Chatbot, um framework escalável e modular de benchmarking baseado em RAG, para avaliar cinco LLMs (GPT-4 Turbo, Claude-3.7 Sonnet, LLaMA-3.3-70B, DeepSeek-R1-Zero e Gemini-2.0 Flash) em cinco domínios: Agricultura, Biologia, Economia, Internet das Coisas (IoT) e Medicina.
Foram usados 50 artigos revisados por pares (10 por domínio) para gerar 250 queries padronizadas, resultando em 1.250 respostas de modelos.
As respostas foram avaliadas em quatro dimensões: similaridade semântica (cosine similarity), tendência de viés/sentimento (VADER), TF-IDF (para detecção lexical de alucinações) e verificação factual baseada em NER.
Resultados: LLaMA-3.3-70B obteve o melhor desempenho geral em todos os domínios.
Palavras-chave
LLMs; RAG; Similaridade Semântica; Detecção de Viés; Detecção de Alucinações; Benchmarking Multidomínio; Elasticsearch.
Metodologia
Base de dados: 50 artigos (10 por domínio). De cada um foram criadas 5 queries → 250 queries.
Processamento:
Extração de texto via PyPDF2, segmentação em chunks (~1000 caracteres, overlap 200).
Embeddings gerados via Sentence-Transformers (all-MiniLM-L6-v2).
Indexação em Elasticsearch (ANN + HNSW graphs).
Execução: Cada query foi usada para recuperar os 5 chunks mais relevantes → enviados a todos os 5 LLMs.
Avaliação:
Similaridade semântica (cosine).
Viés (VADER, polaridade).
Alucinações → TF-IDF + NER.
Normalização (Min-Max + Z-Score).
Resultados
Melhor desempenho: LLaMA-3.3-70B (score final 1.629).
Domínios: LLaMA foi o melhor em todos → Agricultura (0.716), Biologia (0.508), Economia (0.531), IoT (0.957), Medicina (0.671).
Comparação: Claude (1.183) e Gemini (1.060) foram intermediários. GPT-4 Turbo (1.023) e DeepSeek (0.686) ficaram abaixo.
Viés: Todas as respostas tenderam a neutralidade, desejável em textos científicos.
Hallucination detection: DeepSeek mostrou mais problemas em TF-IDF e NER, com maior propensão a alucinações.
Conclusões
O framework fornece avaliação multi-dimensional robusta de LLMs em contextos especializados.
Evidencia que desempenho é altamente dependente do domínio.
LLaMA-3.3-70B mostrou maior consistência em precisão semântica e factualidade.
Limitações: dataset pequeno (50 artigos), foco em inglês, avaliação de alucinações superficial (TF-IDF + NER).
Futuro: ampliar para multilíngue, aumentar corpus, usar validação com especialistas e técnicas mais avançadas de fact-checking.
Linha de pesquisa
➡ Benchmarking multidomínio de LLMs em cenários RAG, com ênfase em alucinações e confiabilidade.
Relevância
⭐ Alta → útil para discussão metodológica no seu referencial sobre avaliação de LLMs em saúde, especialmente no desafio de medir alucinações e factualidade.

🔎 Artigo 41
Título: Privacy-preserving large language models for structured medical information retrieval
Fonte: npj Digital Medicine, 2024 – Wiest et al.
Resumo (traduzido)
Este estudo propõe um pipeline open-source com LLMs locais (Llama 2) para extração de informação clínica estruturada a partir de texto livre.
Tarefa: identificar características de cirrose hepática descompensada em 500 prontuários (MIMIC-IV).
O Llama 2 foi avaliado em versões de 7B, 13B e 70B parâmetros, em zero-shot e one-shot prompting, com validação contra 3 especialistas cegos.
Resultados: modelo de 70B → sensibilidade 100% e especificidade 96% para detecção de cirrose. Também alto desempenho para ascite, dor abdominal, dispneia e confusão.
Palavras-chave
LLM local; Preservação de privacidade; Extração de informação; MIMIC-IV; Cirrose hepática; Prompt engineering.
Metodologia
Dados: 500 histórias médicas do MIMIC-IV (2009–2019).
Features alvo: 5 (cirrose, ascite, dor abdominal, dispneia, confusão).
Modelos testados: Llama-2 7B, 13B, 70B (via llama.cpp).
Prompts:
Zero-shot, one-shot, com definições, chain-of-thought.
Saída forçada em JSON via gramática (garantir padronização).
Ground truth: consenso de 3 médicos.
Avaliação: acurácia, sensibilidade, especificidade, valor preditivo positivo (PPV) e negativo (NPV).
Resultados
70B outperformou os menores:
Cirrose: sensibilidade 100%, especificidade 96%.
Ascite: 95% / 95%.
Dor abdominal: 84% / 97%.
Dispneia: 87% / 97%.
Confusão: 76% / 94%.
One-shot prompting melhorou resultados (ex.: confusão: sensibilidade 88%).
Modelos pequenos (7B) alucinaram mais (falsos positivos).
Prompt engineering ajudou modelos menores; impacto marginal nos maiores.
Conclusões
LLMs locais podem extrair informação clínica com alta precisão, mesmo em zero-shot.
Relevante por ser privacy-preserving (sem envio de dados para cloud).
Confirma que modelos maiores são mais robustos, mas ainda podem falhar em features implícitas.
Futuro: fine-tuning médico, RAG e validação em outros idiomas.
Linha de pesquisa
➡ LLMs locais e preservação de privacidade na extração de informação clínica a partir de texto livre.
Relevância
⭐ Muito alta → combina privacidade (on-premise) + informação clínica estruturada + aplicabilidade real em EHRs → diretamente útil ao seu referencial.

🔎 Artigo 42
Título: Natural language processing of electronic health records for early detection of cognitive decline: a systematic review
Fonte: npj Digital Medicine, 2025 – Shankar et al.
Resumo (traduzido)
Esta revisão sistemática avaliou o uso de processamento de linguagem natural (PLN) para detectar declínio cognitivo em notas clínicas de prontuários eletrônicos (EHRs).
Foram incluídos 18 estudos (n = 1.064.530 pacientes), conduzidos entre 2020 e 2024, majoritariamente nos EUA (78%).
67% utilizaram algoritmos baseados em regras, 28% machine learning tradicional e 17% deep learning.
O desempenho médio dos modelos: sensibilidade 0.88 (IQR 0.74–0.91), especificidade 0.96 (IQR 0.81–0.99).
Modelos de deep learning (ex. BERT, ClinicalBERT) alcançaram AUROC até 0.997, identificando sinais anos antes do diagnóstico formal de MCI.
Desafios: dados incompletos, documentação clínica inconsistente, ausência de validação externa.
Conclusão: NLP é promissor, mas precisa de padronização, datasets anotados e frameworks de implementação equitativos.
Palavras-chave
Declínio cognitivo; Demência; Doença de Alzheimer; Prontuário Eletrônico; NLP; Machine Learning; Deep Learning; Revisão Sistemática; PRISMA.
Metodologia
Diretriz: PRISMA 2020; registrado no PROSPERO (CRD42024601303).
Busca: 8 bases (PubMed, Embase, Web of Science, etc.) + literatura cinzenta, até setembro/2024.
Critérios de inclusão: ≥60 anos, uso de NLP aplicado a notas clínicas de EHRs para cognição/declínio, comparado a referência (testes cognitivos, critérios diagnósticos, biomarcadores, códigos ICD, etc.).
Amostra final: 18 estudos, totalizando >1 milhão de pacientes.
Extração: características dos estudos, dados populacionais, métodos de NLP, métricas (sens, esp, AUC, F1), limitações e fatores de implementação.
Resultados
Abordagens:
Regra-based (67%) → extração de palavras-chave, expressões regulares, ontologias médicas (UMLS, SNOMED).
ML clássico (28%) → regressão logística, random forest, XGBoost.
Deep learning (17%) → ClinicalBERT, ensembles, LLMs clínicos.
Performance:
Detecção de demência estabelecida: sens. 0.91 / esp. 0.97.
Detecção de MCI precoce: sens. 0.76 / esp. 0.89.
Melhor resultado: ClinicalBERT (AUROC 0.997), identificando sinais até 4 anos antes do diagnóstico.
Desafios relatados:
Dependência de códigos ICD imperfeitos como referência (50%).
Falta de validação externa (83%).
Documentação clínica variável (44%).
Possível viés algorítmico (raça, escolaridade, SES).
Conclusões
NLP em EHRs é viável e robusto para detecção de declínio cognitivo, especialmente demência já estabelecida.
Para MCI precoce, os resultados são mais variáveis.
É necessário:
Expandir para multi-modal (texto + exames + demografia).
Garantir equidade e explicabilidade dos modelos.
Criar datasets anotados padronizados.
Avançar para implementação clínica real, além de protótipos de pesquisa.
Linha de pesquisa
➡ NLP em EHRs para detecção precoce de declínio cognitivo e demência, com foco em modelos híbridos e deep learning.
Relevância
⭐ Altíssima → conecta diretamente NLP + EHR + detecção precoce em saúde, servindo como referência metodológica e aplicada ao seu estudo sobre PEPs.

🔎 Artigo 43
Título: A large language model digital patient system enhances ophthalmology history taking skills
Fonte: npj Digital Medicine, 2025 – Luo et al.
Resumo (traduzido)
Este estudo desenvolveu o LLMDP (Large Language Model Digital Patient), um sistema de paciente digital baseado em LLM para treinar estudantes em anamnese oftalmológica.
O sistema converteu EHRs desidentificados em pacientes virtuais com voz e diálogo livre, usando um framework prévio de RAG.
RCT (NCT06229379, N = 84): estudantes treinados com LLMDP tiveram aumento de +10.50 pontos (IC 95%: 4.66–16.33; p < 0.001) em avaliação de anamnese vs. grupo tradicional.
O grupo LLMDP demonstrou também maior empatia e relatou alta satisfação.
Conclusão: LLMs podem oferecer via escalável, segura e de baixo risco para treinamento médico em história clínica.
Palavras-chave
Educação médica; História clínica; Paciente digital; LLM; RAG; Ophthalmology; RCT.
Metodologia
Dataset: 59.343 entradas (EHRs, guidelines, livros, MCQs, diálogos).
Modelo base: Baichuan-13B-Chat (desempenho comparável ao GPT-3.5 Turbo, fine-tuned 20 épocas).
Knowledge Base: 24 doenças oculares, com imagens de lâmpada de fenda.
Sistema: simulação por voz + feedback adaptativo + scoring automático (OSCE).
Validações:
Experimento controlado (n = 50): LLMDP vs. pacientes reais (SPs).
Correlação automática vs. avaliação manual (r = 0.88, p < 0.001).
RCT (n = 84): LLMDP vs. método tradicional.
Resultados
RCT: LLMDP aumentou a média da avaliação (MHTA): 64.62 ± 9.52 vs. 54.12 ± 8.80 (dif. +10.5, p < 0.001).
Melhora em todos os subitens da anamnese (identificação, história da doença, antecedentes).
Empatia: LLMDP → estudantes com melhor desempenho em comunicação empática.
Feedback dos alunos: satisfação alta, apontaram redução de ansiedade, ganho de confiança e economia de custos.
Limitações: ausência de corpo físico (interação parcial), avaliação apenas em oftalmologia, local models menos potentes que GPT-4.
Conclusões
O LLMDP é uma prova robusta de conceito de pacientes digitais baseados em LLMs.
Supera limitações de pacientes padronizados (custos, rigidez, disponibilidade).
Pode ser expandido para outros contextos clínicos.
Futuro: integrar exame físico, imagens médicas e simulação de processos fisiopatológicos.
Linha de pesquisa
➡ Pacientes digitais baseados em LLMs para treinamento clínico em anamnese, integrando empatia e feedback automático.
Relevância
⭐ Alta → embora focado em oftalmologia, mostra o potencial transformador de LLMs na educação médica, aplicável ao treinamento em coleta de dados em PEPs.

🔎 Artigo 44
Título: Development and evaluation of a clinical note summarization system using large language models
Fonte: Communications Medicine, 2025 – Oliveira et al.
Resumo (traduzido)
Contexto: Notas clínicas hospitalares são fontes ricas de informação, mas seu volume e complexidade dificultam a síntese eficiente. Summaries de alta qualidade são essenciais para a continuidade do cuidado e comunicação entre equipes médicas.
Métodos: O estudo desenvolveu um sistema de sumarização de notas de alta hospitalar baseado em LLMs (ex.: GPT-4, Llama 2), dentro da plataforma NoHarm.ai. A construção envolveu:
Levantamento de requisitos com médicos e pacientes (survey e entrevistas).
Integração com prontuários eletrônicos em hospitais brasileiros.
Abordagem híbrida NER + LLM: modelos de NER extraíam entidades-chave (motivo da internação, exames, medicamentos, plano de alta), que eram reformuladas em linguagem natural pelo LLM.
Avaliação via sistema de rating e feedback de médicos (Likert e comentários).
Resultados:
Interpretação diagnóstica próxima do nível humano, com resumos bem avaliados por médicos.
Em 28 pacientes avaliados, 61,5% dos resumos foram “Muito Bons” e 12,8% “Excelentes”, 20,5% “Bons” e apenas 5% “Razoáveis”. Nenhum foi considerado “Ruim”.
Survey com 128 pacientes: 32,8% não receberam documento de alta; 97,7% gostariam de receber um resumo estruturado.
73% dos pacientes se mostraram confortáveis/curiosos com uso de IA na alta hospitalar.
70% dos médicos relataram conforto e 30% muito conforto com IA gerando resumos revisados por médicos.
Principais lacunas apontadas por médicos e pacientes: ausência de medicações suspensas, exames, procedimentos e plano de alta detalhado.
Conclusão: O sistema mostrou-se efetivo e bem aceito por pacientes e médicos, reforçando o potencial dos LLMs em melhorar a comunicação, reduzir tempo de documentação e qualificar a transição do cuidado no contexto do SUS brasileiro.

Palavras-chave
Notas clínicas; Resumo de alta; LLMs; NER; Inteligência Artificial em Saúde; Documentação médica; SUS; NoHarm.ai.

Metodologia
Desenvolvimento participativo:
Equipe multidisciplinar (medicina, ciência da computação, farmácia, engenharia, IA).
Consultas a médicos, residentes, enfermeiros, psicólogos, farmacêuticos e pacientes.
Pipeline técnico:
Extração de entidades com NER (Flair, Lama 2).
Transformação de dados estruturados em linguagem natural.
Reformulação final por LLM, limitado a 4k tokens.
Interface web integrada ao NoHarm.ai → médicos podiam revisar, editar e validar o resumo.
Estrutura do resumo de alta: motivo da internação, diagnósticos, alergias, medicamentos prévios, exames, procedimentos, medicamentos durante a internação, suspensos, condições de alta, plano de alta, prescrição.
Avaliação:
Survey com 128 pacientes (diversos estados do Brasil).
Entrevistas com 10 médicos de atenção primária.
Uso piloto em 28 pacientes → médicos avaliaram clareza, suficiência, acurácia e utilidade.
Feedback coletado em escala Likert + comentários livres.

Resultados
Pacientes (n=128):
62,2% usuários exclusivos do SUS.
32,8% não receberam resumo de alta.
97,7% gostariam de receber.
73,4% abertos/curiosos ao uso de IA.
Médicos (n=10):
100% consideraram o sistema adequado às suas necessidades.
Sugestões: incluir SUS-ID, detalhar resultados laboratoriais, separar medicações prévias das hospitalares, apresentar exames em tabela comparativa.
Testes em 28 pacientes:
Avaliação global: 74% entre Muito Bom e Excelente.
Críticas: 52% consideraram parte da informação insuficiente; 19% excessiva; 30% incorreta.
Adoção inicial: médicos mais propensos a dar feedback quando havia problemas; necessidade de treinamento e integração maior.

Conclusões
LLMs podem gerar resumos de alta hospitalar comparáveis aos humanos, com benefícios claros para comunicação clínica.
A aceitação foi positiva entre pacientes e médicos.
Lacunas: precisão inconsistente, informações redundantes ou faltantes.
Futuro: maior integração ao SUS, validação quantitativa independente, ampliação para outros tipos de documentação (evoluções, prescrições, relatórios).

Linha de pesquisa
➡ Aplicação de LLMs e pipelines híbridos (NER + LLM) para geração de resumos de alta hospitalar em contexto real (SUS).

Relevância
⭐ Altíssima → artigo brasileiro, SUS-contextualizado, sobre sumarização automática de notas clínicas. Dialoga diretamente com:
OMOP / padronização de dados
PEP brasileiro (SUS)
redução da carga administrativa
aceitação clínica e ética em IA

🔎 Artigo 45
Título: Empowering large language models for automated clinical assessment with generation-augmented retrieval and hierarchical chain-of-thought
Fonte: Artificial Intelligence in Medicine, 2025 – Gu et al.
Resumo (traduzido)
Contexto: O uso de EHRs (Prontuários Eletrônicos de Saúde) para avaliação clínica é limitado por processos manuais demorados e exigentes. Embora os LLMs demonstrem forte capacidade em NLP, sua aplicação direta em tarefas clínicas específicas ainda enfrenta barreiras (alucinações, contexto limitado, falta de conhecimento médico).
Métodos: Foi proposto o GAPrompt, um paradigma de prompting composto por cinco etapas:
Seleção de LLMs orientada por prompt (avaliar qual modelo é mais adequado para a tarefa).
Construção de base de conhecimento aumentada por geração (guidelines + exemplos validados por especialistas → NIHSS).
Summary-based Generation-Augmented Retrieval (SGAR) → recuperação mais precisa de critérios e exemplos.
Hierarchical Chain-of-Thought (HCoT) → raciocínio hierárquico (macro sequência + micro CoT frase a frase).
Ensembling de múltiplas gerações → integração de respostas diversas, reduzindo alucinações.
Resultados:
O GAPrompt permitiu que LLMs genéricos alcançassem desempenho próximo de modelos especializados na avaliação quantitativa da gravidade de AVC (NIHSS).
Melhorou consistência, reduziu alucinações e aumentou precisão na extração e síntese de informações clínicas.
O HCoT ajudou a superar a limitação de contexto de grandes EHRs, processando-os em blocos menores com raciocínio em cadeia.
Conclusão: O estudo mostra que estratégias avançadas de prompting podem transformar LLMs genéricos em ferramentas aplicáveis a tarefas clínicas específicas, como avaliação automatizada da gravidade de AVC. A abordagem GAPrompt pode ser adaptada para outras aplicações em saúde e em domínios além da medicina.

Palavras-chave
LLM; Avaliação clínica automatizada; EHR; AVC; GAPrompt; Retrieval-augmented; Hierarchical chain-of-thought.

Metodologia
Tarefa: avaliação automática da gravidade de AVC via EHR, usando NIHSS como padrão.
Pipeline GAPrompt:
Seleção de modelo via prompting.
Construção de KB com guidelines NIHSS + exemplos.
SGAR: recuperação baseada em resumos gerados.
HCoT: macro CoT (nível documento) + micro CoT (nível sentença).
Ensemble de múltiplas respostas → robustez.
Ferramentas: LangChain, EHR dataset CSCR.
Avaliação: precisão diagnóstica, robustez, redução de alucinações.

Resultados
GAPrompt superou abordagens diretas com LLMs, oferecendo:
Melhor compreensão de critérios clínicos.
Recuperação mais relevante de exemplos.
Inferência mais precisa (grau de AVC).
Menos alucinações e inconsistências.
Mostrou que genéricos LLMs + prompting avançado ≈ modelos médicos fechados.

Conclusões
Estratégias de prompting (GAPrompt + SGAR + HCoT + ensemble) são alternativas econômicas e eficazes ao fine-tuning.
Possibilitam uso de LLMs em avaliação clínica quantitativa, não só narrativa.
Abrem espaço para automação em outros contextos clínicos e de pesquisa.

Linha de pesquisa
➡ Avanço metodológico: prompting inteligente (GAPrompt + HCoT) para tornar LLMs genéricos capazes de realizar avaliações clínicas quantitativas a partir de EHRs.
Relevância
⭐ Muito alta → contribui metodologicamente para avaliar precisão clínica, reduzir alucinações e adaptar LLMs genéricos ao domínio médico, central no seu referencial.

🔎 Artigo 47
Título: Integration of Multi-Source Medical Data for Medical Diagnosis Question Answering
Fonte: IEEE Transactions on Medical Imaging, 2025 – Peng et al.
Resumo (traduzido)
O trabalho amplia o escopo de QA médico para diagnóstico com múltiplas fontes (MedDQA): perguntas que exigem combinar EHRs, exames laboratoriais, imagens radiológicas, imagens patológicas e dados clínicos. Os autores constroem um grande dataset real (250.917 pacientes; 768.183 imagens; 25 especialidades) e geram 630.827 pares Q&A com LLM (validados por especialistas + filtro automático). Propõem o MMA (Medical Multi-Agent), um sistema multiagente baseado em LLM, com papéis especializados (clínico geral, laboratorista, radiologista, patologista) que processam suas modalidades e convertem tudo em texto estruturado para fusão e resposta final. Resultados mostram que a arquitetura melhora a capacidade de diagnosticar a partir de dados multi-fonte, estabelecendo um baseline robusto para pesquisas futuras.
Palavras-chave
MedDQA; QA médico; dados multi-fonte; multi-agente; LLM; radiologia; patologia; laboratório; EHR.
Metodologia
Tarefa: MedDQA (QA de diagnóstico com múltiplas fontes).
Dataset: 250.917 internações; 768.183 imagens (X-ray, CT, MRI, patologia); 25 departamentos; JSON integrando histórico, laudos, exames; 630.827 Q&As (TR, TI, II, MSI).
Geração Q&A: GPT-4 com prompting (papel, tarefa, few-shot, controle de saída); revisão por 3 especialistas; data filtering via classificador treinado em 2.000 pares anotados.
Sistema: MMA (quatro agentes LLM):
GP (clínico): hipótese e exames sugeridos a partir do histórico.
Lab scientist: análise dos exames e relatório.
Radiologist: análise de imagem e laudo.
Pathologist: análise histopatológica e laudo.
Saídas padronizadas em texto → fusão e resposta.
Avaliação: comparação com métodos MedQA/MedVQA single-fonte; métricas de acerto por categoria (TR/TI/II/MSI).
Resultados
MMA supera métodos prévios focados em fonte única, especialmente em Multi-Source Inference (MSI).
A integração textual padronizada de cada agente melhora a robustez do raciocínio clínico e a completude das respostas.
O dataset cobre ampla diversidade (modalidades, órgãos, classes de doença), servindo como benchmark de diagnóstico multi-modal.
Conclusões
Diagnóstico automatizado exige fusão multi-fonte; abordagens só com texto ou só imagem são insuficientes.
O paradigma multiagente + dataset real em larga escala viabiliza avaliação e evolução de modelos para decisão clínica.
Estabelece base para futuros métodos (p.ex., melhor coordenação entre agentes, raciocínio causal, mitigação de alucinações).
Linha de pesquisa
➡ Sistemas multiagentes baseados em LLM para diagnóstico com dados clínicos heterogêneos, incluindo curadoria de grandes datasets multi-fonte e protocolos de avaliação por categorias (TR/TI/II/MSI).
Relevância
⭐ Muito alta — Alinha diretamente com integração de EHR + exames, QA de diagnóstico, orquestração via LLM, e define benchmark público para pesquisas em raciocínio clínico multimodal.

🔎 Artigo 48
Título: Leveraging Large Language Models for Simplified Patient Summary Generation, Literature Retrieval and Medical Information Summarization: A Health CASCADE Study
Fonte: Proceedings of the 57th Hawaii International Conference on System Sciences, 2024 – Balaskas et al.
Resumo (traduzido)
O artigo apresenta um sistema inovador que combina EHRs (via padrão FHIR), LLMs e modelos de recuperação densa (dense retrievers) para gerar sumários simplificados de pacientes (SPS), recuperar literatura acadêmica relevante (PubMed, CORD-19 etc.) e produzir resumos clínicos concisos para apoiar médicos na tomada de decisão. O SPS é integrado à plataforma de prontuário eletrônico com um infobutton interativo, permitindo que médicos façam perguntas, adicionem notas e ajustem os critérios de relevância. O sistema funciona localmente (on-premises), garantindo privacidade e evitando riscos associados a serviços externos.
Palavras-chave
EHR, FHIR, NLP, LLM, resumo de pacientes, recuperação de literatura, privacidade em saúde.
Metodologia
Extração de dados (FHIR): quatro recursos principais — Patient, MedicationRequest, Observation, Condition → convertidos em tripletas semânticas (ex.: [Paciente | Condição | Hipertensão]).
Transformação em SPS: uso do WizardVicuna LLM (WVLLM) para transformar tripletas em sumários naturais (SPS), com checagem iterativa para evitar alucinações.
Recuperação contextual: embeddings via Sentence-Transformers + FAISS → busca em base vetorial (PubMed, CORD-19, WikiDoc etc., 2,75M passagens).
Bi-encoder recupera top 100, cross-encoder reranqueia → top 10 relevantes.
Critérios de relevância: aplicabilidade clínica, alinhamento a objetivos terapêuticos, respaldo em diretrizes, preferências do paciente.
Resumização médica: WVLLM condensa os trechos relevantes → sumários clínicos concisos.
Entrega via EHR: interface integrada → médico vê SPS + resumo de literatura + metadados.
Resultados
Avaliação quantitativa: dataset iCliniq; métricas METEOR e BERTScore.
Comparação com ChatGPT-3.5 e ChatDoctor-13B:
ChatDoctor: F1=0,853 | METEOR=0,210
ChatGPT: F1=0,8495 | METEOR=0,234
SPS System: F1=0,842 | METEOR=0,207 (competitivo, mesmo sem fine-tuning médico).
Sistema mostrou boa precisão e recall, apesar de ainda não ser otimizado para domínio médico.
Conclusões
Integração de LLMs + FHIR + recuperação densa fornece sumários simplificados + literatura relevante em contexto clínico.
Privacidade garantida por rodar localmente, evitando riscos de sistemas externos (como ChatGPT em nuvem).
Possibilita colaboração homem-máquina, já que médicos podem refinar SPS via interface interativa.
Futuro: fine-tuning médico, expansão da base de dados, avaliações qualitativas com médicos.
Linha de pesquisa
➡ Resumo automático de pacientes + RAG para suporte clínico baseado em EHRs, com ênfase em privacidade, integração com sistemas hospitalares e interação médico-LLM.
Relevância
⭐ Alta — muito aplicável ao seu referencial, Nath: trata de resumos de pacientes, integração com EHR, recuperação de literatura e RAG.

🔎 Artigo 49
Título: Lessons learned on information retrieval in electronic health records: a comparison of embedding models and pooling strategies
Fonte: Journal of the American Medical Informatics Association (JAMIA), 2025 – Myers et al.
Resumo (traduzido)
Aplicar LLMs a EHRs é desafiador devido ao volume e contexto longo dos registros clínicos. O artigo realiza um estudo de ablação comparando 7 modelos de embeddings (gerais e biomédicos) e estratégias de pooling em 3 tarefas de recuperação clínica (diagnóstico primário, antibióticos e procedimentos) em dois datasets: MIMIC-III e um EHR privado da UW. O objetivo é entender o impacto das escolhas no pipeline de RAG clínico.
Palavras-chave
NLP clínico, embeddings, pooling, RAG, EHR, recuperação de informação.
Metodologia
Tarefas: recuperar (1) diagnóstico primário, (2) antibióticos, (3) procedimentos cirúrgicos.
Datasets: MIMIC-III (público) + UW (privado). Notas segmentadas em chunks (256 tokens).
Modelos testados:
Embeddings: BGE, Gatortron, SFR-Embedding-Mistral, LLM2Vec-Llama-3.
LLMs decoders: Llama-3-8B, Mistral-7B, BioMistral.
Pooling: mean, weighted mean, max, last token, CLS.
Métrica: Mean Average Precision (MAP).
Configurações testadas: 3.488 (diferentes queries, datasets, pooling).
Resultados
BGE (modelo geral pequeno) superou todos os outros, mesmo biomédicos como Gatortron e BioMistral (MAP: 0,403 UW; 0,475 MIMIC).
Pooling: mean/weighted mean geralmente melhores que last/CLS.
Query phrasing importa muito: pequenas variações (“primary diagnosis” vs. “patient’s primary diagnosis”) mudaram drasticamente a performance.
Modelos médicos (Gatortron, BioMistral) tiveram desempenho inferior ao esperado.
Variabilidade maior em UW do que em MIMIC → sensibilidade a domínio e instituição.
Conclusões
Escolha do embedding + pooling + query impacta fortemente a performance em RAG clínico.
Benchmarks genéricos (MTEB) não transferem bem para EHRs.
Necessário ajuste específico por instituição e validação empírica local.
Fornece guia prático para design de retrievers clínicos.
Linha de pesquisa
➡ Otimização de componentes de RAG clínico (retrievers, embeddings, pooling) para EHRs, com foco em robustez e portabilidade institucional.
Relevância
⭐ Muito alta — diretamente aplicável ao seu referencial sobre RAG em saúde, pois traz evidência empírica detalhada sobre retrievers em EHR.

🔎 Artigo 50
Título: Adapting Generative Large Language Models for Information Extraction from Unstructured Electronic Health Records in Residential Aged Care: A Comparative Analysis of Training Approaches
Fonte: Journal of Healthcare Informatics Research, 2025 – Vithanage et al.
Resumo (traduzido)
O estudo compara diferentes abordagens de treinamento de LLMs generativos (Llama 3.1-8B) para extração de informação (IE) de notas clínicas não estruturadas em instituições de cuidados prolongados (RACFs) na Austrália. O foco foi em NER para identificar entidades relacionadas a agitação em demência e fatores de risco para desnutrição. Foram avaliados zero-shot e few-shot prompting, com e sem fine-tuning eficiente (PEFT/LoRA) e com/sem RAG.
Principais resultados:
Few-shot > zero-shot quando usados isoladamente.
PEFT melhora significativamente ambos (zero/few-shot), permitindo que o zero-shot com PEFT alcance desempenho comparável ao few-shot.
RAG melhora apenas no few-shot, não no zero-shot.
Few-shot + RAG ≈ zero-shot + PEFT, indicando que diferentes caminhos chegam a desempenho semelhante.
Métricas usadas: accuracy, precision, recall, F1, com análise estatística não-paramétrica.
Palavras-chave
LLMs, informação clínica, NER, EHR não estruturado, aged care, RAG, PEFT.
Metodologia
Dataset real: notas de enfermagem (2019–2021) de 40 RACFs australianos, com anotações sobre demência e nutrição (83 e 37 entidades, respectivamente).
Modelos: Llama 3.1-8B.
Treinamento: zero-shot e few-shot prompts; aplicação de PEFT (LoRA) e RAG.
Infraestrutura: 4 GPUs NVIDIA RTX A5000 (24GB).
Divisão de dados: 80% treino, 10% validação, 10% teste.
Resultados
PEFT foi o fator mais consistente de ganho → reduziu dependência de exemplos.
RAG só trouxe ganhos em few-shot, não no zero-shot.
Zero-shot + PEFT ≈ Few-shot + RAG, sugerindo equivalência prática.
Demonstra aplicabilidade real em notas clínicas complexas, onde terminologia é ambígua.
Conclusões
PEFT é essencial para adaptação eficiente de LLMs clínicos em IE.
RAG deve ser combinado com few-shot, pois sozinho não melhora no zero-shot.
Traz insights sobre como escolher estratégias de treinamento conforme contexto.
Relevante para IE em saúde, NER clínico e adaptação prática de LLMs em EHRs não estruturados.
Linha de pesquisa
➡ Comparação sistemática de estratégias (prompting, PEFT, RAG) para IE em EHR não estruturados, com foco em notas de enfermagem e domínios geriátricos.
Relevância
⭐ Muito alta — articula bem com seus eixos de IE, NER, RAG e adaptação de LLMs em EHRs clínicos reais.

🔎 Artigo 51
Título: The Effectiveness of Large Language Models in Transforming Unstructured Text to Standardized Formats
Fonte: IEEE Access, 2025 – Brach, Košťál & Ries
Resumo (traduzido)
O trabalho avalia a capacidade de LLMs de converter texto não estruturado em formatos padronizados. O caso de uso é receitas de cozinha → formato Cooklang, mas os autores ressaltam aplicações em saúde (HL7), documentação técnica e contratos legais. Foram testados 4 modelos (GPT-4o, GPT-4o-mini, Llama3.3:70B, Llama3.1:8B), com diferentes técnicas de prompting (zero-shot, few-shot, MIPROv2).
Palavras-chave
LLMs, texto estruturado, padronização, HL7, Cooklang, few-shot learning, prompt engineering.
Metodologia
Dataset: 1.098 receitas (Cooklang).
Modelos: GPT-4o, GPT-4o-mini, Llama3.3:70B, Llama3.1:8B.
Técnicas: zero-shot, few-shot, MIPROv2.
Métricas: WER (Word Error Rate), ROUGE-L, TER (Token Error Rate), além de métricas de identificação semântica (ingredientes, unidades, quantidades).
Cenários testados: (1) apenas texto, (2) texto + ingredientes, (3) texto + ingredientes + schema Cooklang.
Resultados
GPT-4o (few-shot) alcançou melhor performance (ROUGE-L=0.8209; WER=0.3509).
Modelos menores (Llama 3.1:8B) mostraram bom potencial de otimização via fine-tuning, surpreendendo frente a modelos maiores.
MIPROv2 melhorou consistência estrutural, mas aumentou custo computacional.
Desempenho escala com tamanho do modelo, mas nem sempre linear.
Conclusões
LLMs podem transformar texto não estruturado em formatos estruturados confiáveis.
Pouco ou nenhum treinamento adicional pode ser suficiente (few-shot já gera bom desempenho).
Potenciais aplicações além da culinária → saúde (HL7), documentação técnica, registros clínicos estruturados.
Fornece framework de avaliação com métricas gerais + específicas.
Linha de pesquisa
➡ Conversão de texto não estruturado para formatos padronizados (HL7, EHRs, documentação técnica) usando LLMs + prompting.
Relevância
⭐ Alta — embora aplicado a receitas, o artigo é fundacional para o tema de padronização em saúde (ex.: HL7, OMOP), diretamente ligado à transformação de texto clínico em formatos estruturados.





Referencias:
QIN, Suyang et al. Intelligent Chinese patent medicine (CPM) recommendation framework: Integrating large language models, retrieval-augmented generation, and the largest CPM dataset. Pharmacological Research, v. 219, p. 107883, 2025. ISSN 1043-6618. Disponível em: https://doi.org/10.1016/j.phrs.2025.107883. Acesso em: 31 ago. 2025.
XU, Dexuan et al. Knowledge fusion in deep learning-based medical vision-language models: A review. Information Fusion, v. 125, p. 103455, 2026. ISSN 1566-2535. Disponível em: https://doi.org/10.1016/j.inffus.2025.103455. Acesso em: 31 ago. 2025.
GAO, Yanjun et al. Leveraging Medical Knowledge Graphs Into Large Language Models for Diagnosis Prediction: Design and Application Study. JMIR AI, v. 4, 2025. ISSN 2817-1705. Disponível em: https://doi.org/10.2196/58670. Acesso em: 31 ago. 2025.
CHOW, James C. L.; LI, Kay. Developing Effective Frameworks for Large Language Model–Based Medical Chatbots: Insights From Radiotherapy Education With ChatGPT. JMIR Cancer, v. 11, 2025. ISSN 2369-1999. Disponível em: https://doi.org/10.2196/66633. Acesso em: 31 ago. 2025.
GU, Zhanzhong et al. Empowering large language models for automated clinical assessment with generation-augmented retrieval and hierarchical chain-of-thought. Artificial Intelligence in Medicine, v. 162, p. 103078, 2025. ISSN 0933-3657. Disponível em: https://doi.org/10.1016/j.artmed.2025.103078. Acesso em: 31 ago. 2025.
ZOU, Haochen; WANG, Yongli; HUANG, Anqi. A novel domain knowledge augmented large language model based medical conversation system for sustainable smart city development. Sustainable Cities and Society, v. 128, p. 106444, 2025. ISSN 2210-6707. Disponível em: https://doi.org/10.1016/j.scs.2025.106444. Acesso em: 31 ago. 2025.
ARZIDEH, Kamyar et al. From BERT to generative AI - Comparing encoder-only vs. large language models in a cohort of lung cancer patients for named entity recognition in unstructured medical reports. Computers in Biology and Medicine, v. 195, p. 110665, 2025. ISSN 0010-4825. Disponível em: https://doi.org/10.1016/j.compbiomed.2025.110665. Acesso em: 31 ago. 2025.
AYESHA, Noor et al. Large Language Model in Healthcare for the Prediction of Genetic Variants from Unstructured Text Medicine Data Using Natural Language Processing. Computers, Materials and Continua, v. 84, n. 1, p. 1883-1899, 2025. ISSN 1546-2218. Disponível em: https://doi.org/10.32604/cmc.2025.063560. Acesso em: 31 ago. 2025.
KUNZE, Kyle N. et al. Large Language Models Applied to Health Care Tasks May Improve Clinical Efficiency, Value of Care Rendered, Research, and Medical Education. Arthroscopy: The Journal of Arthroscopic & Related Surgery, v. 41, n. 3, p. 547-556, 2025. ISSN 0749-8063. Disponível em: https://doi.org/10.1016/j.arthro.2024.12.010. Acesso em: 31 ago. 2025.
GILANI, Komal et al. CDE-Mapper: Using retrieval-augmented language models for linking clinical data elements to controlled vocabularies. Computers in Biology and Medicine, v. 196, Part B, p. 110745, 2025. ISSN 0010-4825. Disponível em: https://doi.org/10.1016/j.compbiomed.2025.110745. Acesso em: 31 ago. 2025.
LI, Ronghao et al. Enhancing Pulmonary Disease Prediction Using Large Language Models With Feature Summarization and Hybrid Retrieval-Augmented Generation: Multicenter Methodological Study Based on Radiology Report. Journal of Medical Internet Research, v. 27, 2025. ISSN 1438-8871. Disponível em: https://doi.org/10.2196/72638. Acesso em: 31 ago. 2025.
STOLL, Dragan et al. Case reports unlocked: Leveraging retrieval-augmented generation with large language models to advance research on psychological child maltreatment. Child Abuse & Neglect, v. 169, Part 2, p. 107653, 2025. ISSN 0145-2134. Disponível em: https://doi.org/10.1016/j.chiabu.2025.107653. Acesso em: 31 ago. 2025.
VITHANAGE, Dinithi et al. A comprehensive evaluation of large language models for information extraction from unstructured electronic health records in residential aged care. Computers in Biology and Medicine, v. 197, Part A, p. 111013, 2025. ISSN 0010-4825. Disponível em: https://doi.org/10.1016/j.compbiomed.2025.111013. Acesso em: 31 ago. 2025.
LI, Xiang et al. Vision-Language Models in medical image analysis: From simple fusion to general large models. Information Fusion, v. 118, p. 102995, 2025. ISSN 1566-2535. Disponível em: https://doi.org/10.1016/j.inffus.2025.102995. Acesso em: 31 ago. 2025.
WU, Yuzhou et al. Contrastive learning with large language models for medical code prediction. Expert Systems with Applications, v. 277, p. 127241, 2025. ISSN 0957-4174. Disponível em: https://doi.org/10.1016/j.eswa.2025.127241. Acesso em: 31 ago. 2025.
LI, Qiyuan et al. Reviewing clinical knowledge in medical large language models: Training and beyond. Knowledge-Based Systems, v. 328, p. 114215, 2025. ISSN 0950-7051. Disponível em: https://doi.org/10.1016/j.knosys.2025.114215. Acesso em: 31 ago. 2025.
CHEN, Yubo et al. Prompt robust large language model for Chinese medical named entity recognition. Information Processing & Management, v. 62, n. 5, p. 104189, 2025. ISSN 0306-4573. Disponível em: https://doi.org/10.1016/j.ipm.2025.104189. Acesso em: 31 ago. 2025.
BARTELS, Stefan; CARUS, Jasmin. From text to data: Open-source large language models in extracting cancer related medical attributes from German pathology reports. International Journal of Medical Informatics, v. 203, p. 106022, 2025. ISSN 1386-5056. Disponível em: https://doi.org/10.1016/j.ijmedinf.2025.106022. Acesso em: 31 ago. 2025.
XU, Xiaowei et al. Development and evaluation of a retrieval-augmented large language model framework for enhancing endodontic education. International Journal of Medical Informatics, v. 203, p. 106006, 2025. ISSN 1386-5056. Disponível em: https://doi.org/10.1016/j.ijmedinf.2025.106006. Acesso em: 31 ago. 2025.
DENG, Yiyan et al. MedKA: A knowledge graph-augmented approach to improve factuality in medical Large Language Models. Journal of Biomedical Informatics, v. 168, p. 104871, 2025. ISSN 1532-0464. Disponível em: https://doi.org/10.1016/j.jbi.2025.104871. Acesso em: 31 ago. 2025.
BAZOGE, Adrien et al. Assessing large language models for acute heart failure classification and information extraction from French clinical notes. Computers in Biology and Medicine, v. 195, p. 110609, 2025. ISSN 0010-4825. Disponível em: https://doi.org/10.1016/j.compbiomed.2025.110609. Acesso em: 31 ago. 2025.
REN, Tengfei et al. Retrieval-Augmented Generation-aided causal identification of aviation accidents: A large language model methodology. Expert Systems with Applications, v. 278, p. 127306, 2025. ISSN 0957-4174. Disponível em: https://doi.org/10.1016/j.eswa.2025.127306. Acesso em: 31 ago. 2025.
PAN, Jie et al. Integrating large language models with human expertise for disease detection in electronic health records. Computers in Biology and Medicine, v. 191, p. 110161, 2025. ISSN 0010-4825. Disponível em: https://doi.org/10.1016/j.compbiomed.2025.110161. Acesso em: 31 ago. 2025.
CHEN, Haihua et al. Enhancing data quality in medical concept normalization through large language models. Journal of Biomedical Informatics, v. 165, p. 104812, 2025. ISSN 1532-0464. Disponível em: https://doi.org/10.1016/j.jbi.2025.104812. Acesso em: 31 ago. 2025.
LI, Xueshen et al. A two-stage proactive dialogue generator for efficient clinical information collection using large language model. Expert Systems with Applications, v. 287, p. 127833, 2025. ISSN 0957-4174. Disponível em: https://doi.org/10.1016/j.eswa.2025.127833. Acesso em: 31 ago. 2025.
YAO, Guanhong et al. Comparing the accuracy of large language models and prompt engineering in diagnosing realworld cases. International Journal of Medical Informatics, v. 203, p. 106026, 2025. ISSN 1386-5056. Disponível em: https://doi.org/10.1016/j.ijmedinf.2025.106026. Acesso em: 31 ago. 2025.
HOU, Yu et al. Improving Dietary Supplement Information Retrieval: Development of a Retrieval-Augmented Generation System With Large Language Models. Journal of Medical Internet Research, v. 27, 2025. ISSN 1438-8871. Disponível em: https://doi.org/10.2196/67677. Acesso em: 31 ago. 2025.
MOËLL, Birger; ARONSSON, Fredrik Sand. Harm Reduction Strategies for Thoughtful Use of Large Language Models in the Medical Domain: Perspectives for Patients and Clinicians. Journal of Medical Internet Research, v. 27, 2025. ISSN 1438-8871. Disponível em: https://doi.org/10.2196/75849. Acesso em: 31 ago. 2025.
LI, Yanqiu et al. The actual performance of large language models in providing liver cirrhosis-related information: A comparative study. International Journal of Medical Informatics, v. 201, p. 105961, 2025. ISSN 1386-5056. Disponível em: https://doi.org/10.1016/j.ijmedinf.2025.105961. Acesso em: 31 ago. 2025.
ZHANG, Kuo et al. Revolutionizing Health Care: The Transformative Impact of Large Language Models in Medicine. Journal of Medical Internet Research, v. 27, 2025. ISSN 1438-8871. Disponível em: https://doi.org/10.2196/59069. Acesso em: 31 ago. 2025.
ADAMS, Meredith C. B. et al. Breaking Digital Health Barriers Through a Large Language Model–Based Tool for Automated Observational Medical Outcomes Partnership Mapping: Development and Validation Study. Journal of Medical Internet Research, v. 27, 2025. ISSN 1438-8871. Disponível em: https://doi.org/10.2196/69004. Acesso em: 31 ago. 2025.
KIM, Hyun-Woo et al. Assessing the performance of ChatGPT's responses to questions related to epilepsy: A cross-sectional study on natural language processing and medical information retrieval. Seizure: European Journal of Epilepsy, v. 114, p. 1-8, 2024. ISSN 1059-1311. Disponível em: https://doi.org/10.1016/j.seizure.2023.11.013. Acesso em: 31 ago. 2025.
HE, Guoxiu; HUANG, Chen. Few-shot medical relation extraction via prompt tuning enhanced pre-trained language model. Neurocomputing, v. 633, p. 129752, 2025. ISSN 0925-2312. Disponível em: https://doi.org/10.1016/j.neucom.2025.129752. Acesso em: 31 ago. 2025.
ZHUANG, Yan et al. Autonomous International Classification of Diseases Coding Using Pretrained Language Models and Advanced Prompt Learning Techniques: Evaluation of an Automated Analysis System Using Medical Text. JMIR Medical Informatics, v. 13, 2025. ISSN 2291-9694. Disponível em: https://doi.org/10.2196/63020. Acesso em: 31 ago. 2025.
XU, Qinwei et al. Towards normalized clinical information extraction in Chinese radiology report with large language models. Expert Systems with Applications, v. 271, p. 126585, 2025. ISSN 0957-4174. Disponível em: https://doi.org/10.1016/j.eswa.2025.126585. Acesso em: 31 ago. 2025.
GUAN, Hao; NOVOA-LAURENTIEV, John; ZHOU, Li. CD-Tron: Leveraging large clinical language model for early detection of cognitive decline from electronic health records. Journal of Biomedical Informatics, v. 166, p. 104830, 2025. ISSN 1532-0464. Disponível em: https://doi.org/10.1016/j.jbi.2025.104830. Acesso em: 31 ago. 2025.
GHORBIAN, Mohsen et al. Transforming breast cancer diagnosis and treatment with large language Models: A comprehensive survey. Methods, v. 239, p. 85-110, 2025. ISSN 1046-2023. Disponível em: https://doi.org/10.1016/j.ymeth.2025.04.001. Acesso em: 31 ago. 2025.
GUAN, Xinxin et al. Summary report auto-generation based on hierarchical corpus using large language model. Displays, v. 89, p. 103055, 2025. ISSN 0141-9382. Disponível em: https://doi.org/10.1016/j.displa.2025.103055. Acesso em: 31 ago. 2025.
SEO, Sujeong; KIM, Kyuli; YANG, Heyoung. Performance Assessment of Large Language Models in Medical Consultation: Comparative Study. JMIR Medical Informatics, v. 13, 2025. ISSN 2291-9694. Disponível em: https://doi.org/10.2196/64318. Acesso em: 31 ago. 2025.
KRAIŠNIKOVIĆ, Ceca et al. Fine-tuning language model embeddings to reveal domain knowledge: An explainable artificial intelligence perspective on medical decision making. Engineering Applications of Artificial Intelligence, v. 139, Part B, p. 109561, 2025. ISSN 0952-1976. Disponível em: https://doi.org/10.1016/j.engappai.2024.109561. Acesso em: 31 ago. 2025.
SHA, Hangyu et al. Leveraging Retrieval-Augmented Large Language Models for Dietary Recommendations With Traditional Chinese Medicine’s Medicine Food Homology: Algorithm Development and Validation. JMIR Medical Informatics, v. 13, 2025. ISSN 2291-9694. Disponível em: https://doi.org/10.2196/75279. Acesso em: 31 ago. 2025.
HUANG, Zhenyu et al. A survey on biomedical automatic text summarization with large language models. Information Processing & Management, v. 62, n. 5, p. 104216, 2025. ISSN 0306-4573. Disponível em: https://doi.org/10.1016/j.ipm.2025.104216. Acesso em: 31 ago. 2025.
CHEN, Guanyu et al. Knowledge graph and large language model integration with focus on educational applications: A survey. Neurocomputing, v. 654, p. 131230, 2025. ISSN 0925-2312. Disponível em: https://doi.org/10.1016/j.neucom.2025.131230. Acesso em: 31 ago. 2025.
LIN, Chihung; KUO, Chang-Fu. Roles and Potential of Large Language Models in Healthcare: A Comprehensive Review. Biomedical Journal, p. 100868, 2025. ISSN 2319-4170. Disponível em: https://doi.org/10.1016/j.bj.2025.100868. Acesso em: 31 ago. 2025.
MENG, Xiangbin et al. The application of large language models in medicine: A scoping review. iScience, v. 27, n. 5, p. 109713, 2024. ISSN 2589-0042. Disponível em: https://doi.org/10.1016/j.isci.2024.109713. Acesso em: 31 ago. 2025.
LUO, Xufei et al. Potential Roles of Large Language Models in the Production of Systematic Reviews and Meta-Analyses. Journal of Medical Internet Research, v. 26, 2024. ISSN 1438-8871. Disponível em: https://doi.org/10.2196/56780. Acesso em: 31 ago. 2025.
WYSOCKA, Magdalena et al. Large Language Models, scientific knowledge and factuality: A framework to streamline human expert evaluation. Journal of Biomedical Informatics, v. 158, p. 104724, 2024. ISSN 1532-0464. Disponível em: https://doi.org/10.1016/j.jbi.2024.104724. Acesso em: 31 ago. 2025.
ZONG, Hui et al. Large Language Models in Worldwide Medical Exams: Platform Development and Comprehensive Analysis. Journal of Medical Internet Research, v. 26, 2024. ISSN 1438-8871. Disponível em: https://doi.org/10.2196/66114. Acesso em: 31 ago. 2025.
ALSAAD, Rawan et al. Multimodal Large Language Models in Health Care: Applications, Challenges, and Future Outlook. Journal of Medical Internet Research, v. 26, 2024. ISSN 1438-8871. Disponível em: https://doi.org/10.2196/59505. Acesso em: 31 ago. 2025.
LU, Qiuhao et al. Enhancing Clinical Relevance of Pretrained Language Models Through Integration of External Knowledge: Case Study on Cardiovascular Diagnosis From Electronic Health Records. JMIR AI, v. 3, 2024. ISSN 2817-1705. Disponível em: https://doi.org/10.2196/56932. Acesso em: 31 ago. 2025.
LIÉVIN, Valentin et al. Can large language models reason about medical questions? Patterns, v. 5, n. 3, p. 100943, 2024. ISSN 2666-3899. Disponível em: https://doi.org/10.1016/j.patter.2024.100943. Acesso em: 31 ago. 2025.
HE, Zhe et al. Quality of Answers of Generative Large Language Models Versus Peer Users for Interpreting Laboratory Test Results for Lay Patients: Evaluation Study. Journal of Medical Internet Research, v. 26, 2024. ISSN 1438-8871. Disponível em: https://doi.org/10.2196/56655. Acesso em: 31 ago. 2025.
YOON, Dukyong et al. Redefining Health Care Data Interoperability: Empirical Exploration of Large Language Models in Information Exchange. Journal of Medical Internet Research, v. 26, 2024. ISSN 1438-8871. Disponível em: https://doi.org/10.2196/56614. Acesso em: 31 ago. 2025.
YE, Cheng. Exploring a learning-to-rank approach to enhance the Retrieval Augmented Generation (RAG)-based electronic medical records search engines. Informatics and Health, v. 1, n. 2, p. 93-99, 2024. ISSN 2949-9534. Disponível em: https://doi.org/10.1016/j.infoh.2024.07.001. Acesso em: 31 ago. 2025.
JUNG, Yoonhwa et al. VisualSiteDiary: A detector-free Vision-Language Transformer model for captioning photologs for daily construction reporting and image retrievals. Automation in Construction, v. 165, p. 105483, 2024. ISSN 0926-5805. Disponível em: https://doi.org/10.1016/j.autcon.2024.105483. Acesso em: 31 ago. 2025.
TAYLOR, Niall et al. Developing healthcare language model embedding spaces. Artificial Intelligence in Medicine, v. 158, p. 103009, 2024. ISSN 0933-3657. Disponível em: https://doi.org/10.1016/j.artmed.2024.103009. Acesso em: 31 ago. 2025.
DU, Xinsong et al. Enhancing early detection of cognitive decline in the elderly: a comparative study utilizing large language models in clinical notes. eBioMedicine, v. 109, p. 105401, 2024. ISSN 2352-3964. Disponível em: https://doi.org/10.1016/j.ebiom.2024.105401. Acesso em: 31 ago. 2025.
DAI, Yizheng et al. TCMChat: A generative large language model for traditional Chinese medicine. Pharmacological Research, v. 210, p. 107530, 2024. ISSN 1043-6618. Disponível em: https://doi.org/10.1016/j.phrs.2024.107530. Acesso em: 31 ago. 2025.
MACIEJEWSKI, Cezary et al. AssistMED project: Transforming cardiology cohort characterisation from electronic health records through natural language processing – Algorithm design, preliminary results, and field prospects. International Journal of Medical Informatics, v. 185, p. 105380, 2024. ISSN 1386-5056. Disponível em: https://doi.org/10.1016/j.ijmedinf.2024.105380. Acesso em: 31 ago. 2025.
ZHANG, Yutong et al. Potential of multimodal large language models for data mining of medical images and free-text reports. Meta-Radiology, v. 2, n. 4, p. 100103, 2024. ISSN 2950-1628. Disponível em: https://doi.org/10.1016/j.metrad.2024.100103. Acesso em: 31 ago. 2025.
GOSWAMI, Joyeeta et al. Parameter-efficient fine-tuning large language model approach for hospital discharge paper summarization. Applied Soft Computing, v. 157, p. 111531, 2024. ISSN 1568-4946. Disponível em: https://doi.org/10.1016/j.asoc.2024.111531. Acesso em: 31 ago. 2025.
SUPRIYONO et al. Advancements in natural language processing: Implications, challenges, and future directions. Telematics and Informatics Reports, v. 16, p. 100173, 2024. ISSN 2772-5030. Disponível em: https://doi.org/10.1016/j.teler.2024.100173. Acesso em: 31 ago. 2025.
ZHANG, Chonghuan et al. SynAsk: unleashing the power of large language models in organic synthesis. Chemical Science, v. 16, n. 1, p. 43-56, 2024. ISSN 2041-6520. Disponível em: https://doi.org/10.1039/d4sc04757e. Acesso em: 31 ago. 2025.
AFSHAR, Majid et al. On the role of the UMLS in supporting diagnosis generation proposed by Large Language Models. Journal of Biomedical Informatics, v. 157, p. 104707, 2024. ISSN 1532-0464. Disponível em: https://doi.org/10.1016/j.jbi.2024.104707. Acesso em: 31 ago. 2025.
CARROLL, Alexander J.; BORYCZ, Joshua. Integrating large language models and generative artificial intelligence tools into information literacy instruction. The Journal of Academic Librarianship, v. 50, n. 4, p. 102899, 2024. ISSN 0099-1333. Disponível em: https://doi.org/10.1016/j.acalib.2024.102899. Acesso em: 31 ago. 2025.
LEE, You-Qian et al. Unlocking the Secrets Behind Advanced Artificial Intelligence Language Models in Deidentifying Chinese-English Mixed Clinical Text: Development and Validation Study. Journal of Medical Internet Research, v. 26, 2024. ISSN 1438-8871. Disponível em: https://doi.org/10.2196/48443. Acesso em: 31 ago. 2025.
LIU, Yucheng. Large language models for air transportation: A critical review. Journal of the Air Transport Research Society, v. 2, p. 100024, 2024. ISSN 2941-198X. Disponível em: https://doi.org/10.1016/j.jatrs.2024.100024. Acesso em: 31 ago. 2025.
WANG, Lei et al. Investigating the Impact of Prompt Engineering on the Performance of Large Language Models for Standardizing Obstetric Diagnosis Text: Comparative Study. JMIR Formative Research, v. 8, 2024. ISSN 2561-326X. Disponível em: https://doi.org/10.2196/53216. Acesso em: 31 ago. 2025.




Van Veen, D., Van Uden, C., Blankemeier, L. et al. Adapted large language models can outperform medical experts in clinical text summarization. Nat Med 30, 1134–1142 (2024). Disponível em: https://doi.org/10.1038/s41591-024-02855-5. Acesso em: 31 ago. 2025.
Oliveira, J.D., Santos, H.D.P., Ulbrich, A.H.D.P.S. et al. Development and evaluation of a clinical note summarization system using large language models. Commun Med 5, 376 (2025). Disponível em: https://doi.org/10.1038/s43856-025-01091-3. Acesso em: 31 ago. 2025.
Chakraborty, S., Chowdhury, R., Shuvo, S.R. et al. A scalable framework for evaluating multiple language models through cross-domain generation and hallucination detection. Sci Rep 15, 29981 (2025). Disponível em: https://doi.org/10.1038/s41598-025-15203-5. Acesso em: 31 ago. 2025.
Walsh, C.G., Wilimitis, D., Chen, Q. et al. Scalable incident detection via natural language processing and probabilistic language models. Sci Rep 14, 23429 (2024). https://doi.org/10.1038/s41598-024-72756-7. Acesso em: 31 ago. 2025.
Luo, MJ., Bi, S., Pang, J. et al. A large language model digital patient system enhances ophthalmology history taking skills. npj Digit. Med. 8, 502 (2025). https://doi.org/10.1038/s41746-025-01841-6. Acesso em: 31 ago. 2025.
Shen, T., Li, Y., Cao, Y. et al. Rapid deployment of large language model DeepSeek in Chinese hospitals demands a regulatory response. Nat Med (2025). https://doi.org/10.1038/s41591-025-03836-y. Acesso em: 31 ago. 2025.
Gao, H., Wang, K., Yuan, Y. et al. A large language model based pipeline for extracting information from patient complaint and anamnesis in clinical notes for severity assessment. Sci Rep 15, 25345 (2025). https://doi.org/10.1038/s41598-025-07649-4. Acesso em: 31 ago. 2025.
Shankar, R., Bundele, A. & Mukhopadhyay, A. Natural language processing of electronic health records for early detection of cognitive decline: a systematic review. npj Digit. Med. 8, 133 (2025). https://doi.org/10.1038/s41746-025-01527-z. Acesso em: 31 ago. 2025.
Wiest, I.C., Bhat, M., Clusmann, J. et al. Large language models for clinical decision support in gastroenterology and hepatology. Nat Rev Gastroenterol Hepatol (2025). https://doi.org/10.1038/s41575-025-01108-1. Acesso em: 31 ago. 2025.
Wiest, I.C., Ferber, D., Zhu, J. et al. Privacy-preserving large language models for structured medical information retrieval. npj Digit. Med. 7, 257 (2024). https://doi.org/10.1038/s41746-024-01233-2. Acesso em: 31 ago. 2025.
Yang, Q., Zuo, H., Su, R. et al. Dual retrieving and ranking medical large language model with retrieval augmented generation. Sci Rep 15, 18062 (2025). https://doi.org/10.1038/s41598-025-00724-w. Acesso em: 31 ago. 2025.




